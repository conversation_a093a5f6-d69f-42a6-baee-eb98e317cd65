// ------------------------------------------------------------------
// Lato
// ------------------------------------------------------------------
$lato-font-path: './lato';

@mixin lato-font($type, $weight, $style) {
    // Cyrillic: U+0400-04FF, U+0500-052F
    @font-face {
        font-family: 'Odoo Unicode Support Noto';
        src: url('https://fonts.odoocdn.com/fonts/noto/NotoSans-#{$type}.woff2') format('woff2'),
             url('https://fonts.odoocdn.com/fonts/noto/NotoSans-#{$type}.woff') format('woff'),
             url('https://fonts.odoocdn.com/fonts/noto/NotoSans-#{$type}.ttf') format('truetype');
        font-weight: $weight;
        font-style: $style;
        unicode-range: U+0400-04FF, U+0500-052F;
    }
    // Hebrew: U+0590-05FF, U+FB1D-FB4F
    @font-face {
        font-family: 'Odoo Unicode Support Noto';
        src: url('https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-#{$type}.woff2') format('woff2'),
             url('https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-#{$type}.woff') format('woff'),
             url('https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-#{$type}.ttf') format('truetype');
        font-weight: $weight;
        font-style: $style;
        unicode-range: U+0590-05FF, U+FB1D-FB4F;
    }
    // Arabic: U+0600-06FF, U+0750-077F
    @font-face {
        font-family: 'Odoo Unicode Support Noto';
        src: url('https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-#{$type}.woff2') format('woff2'),
             url('https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-#{$type}.woff') format('woff'),
             url('https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-#{$type}.ttf') format('truetype');
        font-weight: $weight;
        font-style: $style;
        unicode-range: U+0600-06FF, U+0750-077F, U+08A0-08FF;
    }

    @font-face {
        font-family: 'Lato';
        src: url('#{$lato-font-path}/Lato-#{$type}-webfont.eot');
        src: url('#{$lato-font-path}/Lato-#{$type}-webfont.eot?#iefix') format('embedded-opentype'),
             url('#{$lato-font-path}/Lato-#{$type}-webfont.woff') format('woff'),
             url('#{$lato-font-path}/Lato-#{$type}-webfont.ttf') format('truetype'),
             url('#{$lato-font-path}/Lato-#{$type}-webfont.svg#Lato') format('svg');
        font-weight: $weight;
        font-style: $style;
    }
    @font-face {
        font-family: 'Lato-#{$type}';
        src: url('#{$lato-font-path}/Lato-#{$type}-webfont.eot');
        src: url('#{$lato-font-path}/Lato-#{$type}-webfont.eot?#iefix') format('embedded-opentype'),
             url('#{$lato-font-path}/Lato-#{$type}-webfont.woff') format('woff'),
             url('#{$lato-font-path}/Lato-#{$type}-webfont.ttf') format('truetype'),
             url('#{$lato-font-path}/Lato-#{$type}-webfont.svg#Roboto') format('svg');
    }
}

@mixin lato-font-pair($type, $weight) {
    @include lato-font('#{$type}', $weight, normal);
    @include lato-font('#{$type}Ita', $weight, italic);
}

@include lato-font-pair('Hai', 100);
@include lato-font-pair('Lig', 300);
@include lato-font-pair('Reg', 400);
@include lato-font-pair('Bol', 700);
@include lato-font-pair('Bla', 900);

// ------------------------------------------------------------------
// Google fonts
// ------------------------------------------------------------------
$google-font-path: './google';

@mixin google-font($family, $type, $weight, $style) {
    @font-face {
        font-family: $family;
        src: url('#{$google-font-path}/#{$family}/#{$family}-#{$type}.ttf') format('truetype');
        font-weight: $weight;
        font-style: $style;
    }
}

@mixin google-font-pair($family) {
    @include google-font('#{$family}', 'Regular', 400, normal);
}

@include google-font-pair('Montserrat');
@include google-font-pair('Open_Sans');
@include google-font-pair('Oswald');
@include google-font-pair('Raleway');
@include google-font-pair('Roboto');
@include google-font-pair('Tajawal');
@include google-font-pair('Fira_Mono');
