define("ace/mode/css_highlight_rules", [
  "require",
  "exports",
  "module",
  "ace/lib/oop",
  "ace/lib/lang",
  "ace/mode/text_highlight_rules",
], function (require, exports, module) {
  "use strict";
  var oop = require("../lib/oop");
  var lang = require("../lib/lang");
  var TextHighlightRules = require("./text_highlight_rules").TextHighlightRules;
  var supportType = (exports.supportType =
    "align-content|align-items|align-self|all|animation|animation-delay|animation-direction|animation-duration|animation-fill-mode|animation-iteration-count|animation-name|animation-play-state|animation-timing-function|backface-visibility|background|background-attachment|background-blend-mode|background-clip|background-color|background-image|background-origin|background-position|background-repeat|background-size|border|border-bottom|border-bottom-color|border-bottom-left-radius|border-bottom-right-radius|border-bottom-style|border-bottom-width|border-collapse|border-color|border-image|border-image-outset|border-image-repeat|border-image-slice|border-image-source|border-image-width|border-left|border-left-color|border-left-style|border-left-width|border-radius|border-right|border-right-color|border-right-style|border-right-width|border-spacing|border-style|border-top|border-top-color|border-top-left-radius|border-top-right-radius|border-top-style|border-top-width|border-width|bottom|box-shadow|box-sizing|caption-side|clear|clip|color|column-count|column-fill|column-gap|column-rule|column-rule-color|column-rule-style|column-rule-width|column-span|column-width|columns|content|counter-increment|counter-reset|cursor|direction|display|empty-cells|filter|flex|flex-basis|flex-direction|flex-flow|flex-grow|flex-shrink|flex-wrap|float|font|font-family|font-size|font-size-adjust|font-stretch|font-style|font-variant|font-weight|hanging-punctuation|height|justify-content|left|letter-spacing|line-height|list-style|list-style-image|list-style-position|list-style-type|margin|margin-bottom|margin-left|margin-right|margin-top|max-height|max-width|max-zoom|min-height|min-width|min-zoom|nav-down|nav-index|nav-left|nav-right|nav-up|opacity|order|outline|outline-color|outline-offset|outline-style|outline-width|overflow|overflow-x|overflow-y|padding|padding-bottom|padding-left|padding-right|padding-top|page-break-after|page-break-before|page-break-inside|perspective|perspective-origin|position|quotes|resize|right|tab-size|table-layout|text-align|text-align-last|text-decoration|text-decoration-color|text-decoration-line|text-decoration-style|text-indent|text-justify|text-overflow|text-shadow|text-transform|top|transform|transform-origin|transform-style|transition|transition-delay|transition-duration|transition-property|transition-timing-function|unicode-bidi|user-select|user-zoom|vertical-align|visibility|white-space|width|word-break|word-spacing|word-wrap|z-index");
  var supportFunction = (exports.supportFunction =
    "rgb|rgba|url|attr|counter|counters");
  var supportConstant = (exports.supportConstant =
    "absolute|after-edge|after|all-scroll|all|alphabetic|always|antialiased|armenian|auto|avoid-column|avoid-page|avoid|balance|baseline|before-edge|before|below|bidi-override|block-line-height|block|bold|bolder|border-box|both|bottom|box|break-all|break-word|capitalize|caps-height|caption|center|central|char|circle|cjk-ideographic|clone|close-quote|col-resize|collapse|column|consider-shifts|contain|content-box|cover|crosshair|cubic-bezier|dashed|decimal-leading-zero|decimal|default|disabled|disc|disregard-shifts|distribute-all-lines|distribute-letter|distribute-space|distribute|dotted|double|e-resize|ease-in|ease-in-out|ease-out|ease|ellipsis|end|exclude-ruby|flex-end|flex-start|fill|fixed|georgian|glyphs|grid-height|groove|hand|hanging|hebrew|help|hidden|hiragana-iroha|hiragana|horizontal|icon|ideograph-alpha|ideograph-numeric|ideograph-parenthesis|ideograph-space|ideographic|inactive|include-ruby|inherit|initial|inline-block|inline-box|inline-line-height|inline-table|inline|inset|inside|inter-ideograph|inter-word|invert|italic|justify|katakana-iroha|katakana|keep-all|last|left|lighter|line-edge|line-through|line|linear|list-item|local|loose|lower-alpha|lower-greek|lower-latin|lower-roman|lowercase|lr-tb|ltr|mathematical|max-height|max-size|medium|menu|message-box|middle|move|n-resize|ne-resize|newspaper|no-change|no-close-quote|no-drop|no-open-quote|no-repeat|none|normal|not-allowed|nowrap|nw-resize|oblique|open-quote|outset|outside|overline|padding-box|page|pointer|pre-line|pre-wrap|pre|preserve-3d|progress|relative|repeat-x|repeat-y|repeat|replaced|reset-size|ridge|right|round|row-resize|rtl|s-resize|scroll|se-resize|separate|slice|small-caps|small-caption|solid|space|square|start|static|status-bar|step-end|step-start|steps|stretch|strict|sub|super|sw-resize|table-caption|table-cell|table-column-group|table-column|table-footer-group|table-header-group|table-row-group|table-row|table|tb-rl|text-after-edge|text-before-edge|text-bottom|text-size|text-top|text|thick|thin|transparent|underline|upper-alpha|upper-latin|upper-roman|uppercase|use-script|vertical-ideographic|vertical-text|visible|w-resize|wait|whitespace|z-index|zero|zoom");
  var supportConstantColor = (exports.supportConstantColor =
    "aliceblue|antiquewhite|aqua|aquamarine|azure|beige|bisque|black|blanchedalmond|blue|blueviolet|brown|burlywood|cadetblue|chartreuse|chocolate|coral|cornflowerblue|cornsilk|crimson|cyan|darkblue|darkcyan|darkgoldenrod|darkgray|darkgreen|darkgrey|darkkhaki|darkmagenta|darkolivegreen|darkorange|darkorchid|darkred|darksalmon|darkseagreen|darkslateblue|darkslategray|darkslategrey|darkturquoise|darkviolet|deeppink|deepskyblue|dimgray|dimgrey|dodgerblue|firebrick|floralwhite|forestgreen|fuchsia|gainsboro|ghostwhite|gold|goldenrod|gray|green|greenyellow|grey|honeydew|hotpink|indianred|indigo|ivory|khaki|lavender|lavenderblush|lawngreen|lemonchiffon|lightblue|lightcoral|lightcyan|lightgoldenrodyellow|lightgray|lightgreen|lightgrey|lightpink|lightsalmon|lightseagreen|lightskyblue|lightslategray|lightslategrey|lightsteelblue|lightyellow|lime|limegreen|linen|magenta|maroon|mediumaquamarine|mediumblue|mediumorchid|mediumpurple|mediumseagreen|mediumslateblue|mediumspringgreen|mediumturquoise|mediumvioletred|midnightblue|mintcream|mistyrose|moccasin|navajowhite|navy|oldlace|olive|olivedrab|orange|orangered|orchid|palegoldenrod|palegreen|paleturquoise|palevioletred|papayawhip|peachpuff|peru|pink|plum|powderblue|purple|rebeccapurple|red|rosybrown|royalblue|saddlebrown|salmon|sandybrown|seagreen|seashell|sienna|silver|skyblue|slateblue|slategray|slategrey|snow|springgreen|steelblue|tan|teal|thistle|tomato|turquoise|violet|wheat|white|whitesmoke|yellow|yellowgreen");
  var supportConstantFonts = (exports.supportConstantFonts =
    "arial|century|comic|courier|cursive|fantasy|garamond|georgia|helvetica|impact|lucida|symbol|system|tahoma|times|trebuchet|utopia|verdana|webdings|sans-serif|serif|monospace");
  var numRe = (exports.numRe =
    "\\-?(?:(?:[0-9]+(?:\\.[0-9]+)?)|(?:\\.[0-9]+))");
  var pseudoElements = (exports.pseudoElements =
    "(\\:+)\\b(after|before|first-letter|first-line|moz-selection|selection)\\b");
  var pseudoClasses = (exports.pseudoClasses =
    "(:)\\b(active|checked|disabled|empty|enabled|first-child|first-of-type|focus|hover|indeterminate|invalid|last-child|last-of-type|link|not|nth-child|nth-last-child|nth-last-of-type|nth-of-type|only-child|only-of-type|required|root|target|valid|visited)\\b");
  var CssHighlightRules = function () {
    var keywordMapper = this.createKeywordMapper(
      {
        "support.function": supportFunction,
        "support.constant": supportConstant,
        "support.type": supportType,
        "support.constant.color": supportConstantColor,
        "support.constant.fonts": supportConstantFonts,
      },
      "text",
      true,
    );
    this.$rules = {
      start: [
        {
          include: ["strings", "url", "comments"],
        },
        {
          token: "paren.lparen",
          regex: "\\{",
          next: "ruleset",
        },
        {
          token: "paren.rparen",
          regex: "\\}",
        },
        {
          token: "string",
          regex: "@(?!viewport)",
          next: "media",
        },
        {
          token: "keyword",
          regex: "#[a-z0-9-_]+",
        },
        {
          token: "keyword",
          regex: "%",
        },
        {
          token: "variable",
          regex: "\\.[a-z0-9-_]+",
        },
        {
          token: "string",
          regex: ":[a-z0-9-_]+",
        },
        {
          token: "constant.numeric",
          regex: numRe,
        },
        {
          token: "constant",
          regex: "[a-z0-9-_]+",
        },
        {
          caseInsensitive: true,
        },
      ],
      media: [
        {
          include: ["strings", "url", "comments"],
        },
        {
          token: "paren.lparen",
          regex: "\\{",
          next: "start",
        },
        {
          token: "paren.rparen",
          regex: "\\}",
          next: "start",
        },
        {
          token: "string",
          regex: ";",
          next: "start",
        },
        {
          token: "keyword",
          regex:
            "(?:media|supports|document|charset|import|namespace|media|supports|document" +
            "|page|font|keyframes|viewport|counter-style|font-feature-values" +
            "|swash|ornaments|annotation|stylistic|styleset|character-variant)",
        },
      ],
      comments: [
        {
          token: "comment", // multi line comment
          regex: "\\/\\*",
          push: [
            {
              token: "comment",
              regex: "\\*\\/",
              next: "pop",
            },
            {
              defaultToken: "comment",
            },
          ],
        },
      ],
      ruleset: [
        {
          regex: "-(webkit|ms|moz|o)-",
          token: "text",
        },
        {
          token: "punctuation.operator",
          regex: "[:;]",
        },
        {
          token: "paren.rparen",
          regex: "\\}",
          next: "start",
        },
        {
          include: ["strings", "url", "comments"],
        },
        {
          token: ["constant.numeric", "keyword"],
          regex:
            "(" +
            numRe +
            ")(ch|cm|deg|em|ex|fr|gd|grad|Hz|in|kHz|mm|ms|pc|pt|px|rad|rem|s|turn|vh|vmax|vmin|vm|vw|%)",
        },
        {
          token: "constant.numeric",
          regex: numRe,
        },
        {
          token: "constant.numeric", // hex6 color
          regex: "#[a-f0-9]{6}",
        },
        {
          token: "constant.numeric", // hex3 color
          regex: "#[a-f0-9]{3}",
        },
        {
          token: [
            "punctuation",
            "entity.other.attribute-name.pseudo-element.css",
          ],
          regex: pseudoElements,
        },
        {
          token: [
            "punctuation",
            "entity.other.attribute-name.pseudo-class.css",
          ],
          regex: pseudoClasses,
        },
        {
          include: "url",
        },
        {
          token: keywordMapper,
          regex: "\\-?[a-zA-Z_][a-zA-Z0-9_\\-]*",
        },
        {
          caseInsensitive: true,
        },
      ],
      url: [
        {
          token: "support.function",
          regex: "(?:url(:?-prefix)?|domain|regexp)\\(",
          push: [
            {
              token: "support.function",
              regex: "\\)",
              next: "pop",
            },
            {
              defaultToken: "string",
            },
          ],
        },
      ],
      strings: [
        {
          token: "string.start",
          regex: "'",
          push: [
            {
              token: "string.end",
              regex: "'|$",
              next: "pop",
            },
            {
              include: "escapes",
            },
            {
              token: "constant.language.escape",
              regex: /\\$/,
              consumeLineEnd: true,
            },
            {
              defaultToken: "string",
            },
          ],
        },
        {
          token: "string.start",
          regex: '"',
          push: [
            {
              token: "string.end",
              regex: '"|$',
              next: "pop",
            },
            {
              include: "escapes",
            },
            {
              token: "constant.language.escape",
              regex: /\\$/,
              consumeLineEnd: true,
            },
            {
              defaultToken: "string",
            },
          ],
        },
      ],
      escapes: [
        {
          token: "constant.language.escape",
          regex: /\\([a-fA-F\d]{1,6}|[^a-fA-F\d])/,
        },
      ],
    };
    this.normalizeRules();
  };
  oop.inherits(CssHighlightRules, TextHighlightRules);
  exports.CssHighlightRules = CssHighlightRules;
});

define("ace/mode/scss_highlight_rules", [
  "require",
  "exports",
  "module",
  "ace/lib/oop",
  "ace/lib/lang",
  "ace/mode/text_highlight_rules",
  "ace/mode/css_highlight_rules",
], function (require, exports, module) {
  "use strict";
  var oop = require("../lib/oop");
  var lang = require("../lib/lang");
  var TextHighlightRules = require("./text_highlight_rules").TextHighlightRules;
  var CssHighlightRules = require("./css_highlight_rules");
  var ScssHighlightRules = function () {
    var properties = lang.arrayToMap(CssHighlightRules.supportType.split("|"));
    var functions = lang.arrayToMap(
      (
        "hsl|hsla|rgb|rgba|url|attr|counter|counters|abs|adjust_color|adjust_hue|" +
        "alpha|join|blue|ceil|change_color|comparable|complement|darken|desaturate|" +
        "floor|grayscale|green|hue|if|invert|join|length|lighten|lightness|mix|" +
        "nth|opacify|opacity|percentage|quote|red|round|saturate|saturation|" +
        "scale_color|transparentize|type_of|unit|unitless|unquote"
      ).split("|"),
    );
    var constants = lang.arrayToMap(
      CssHighlightRules.supportConstant.split("|"),
    );
    var colors = lang.arrayToMap(
      CssHighlightRules.supportConstantColor.split("|"),
    );
    var keywords = lang.arrayToMap(
      "@mixin|@extend|@include|@import|@media|@debug|@warn|@if|@for|@each|@while|@else|@font-face|@-webkit-keyframes|if|and|!default|module|def|end|declare".split(
        "|",
      ),
    );
    var tags = lang.arrayToMap(
      (
        "a|abbr|acronym|address|applet|area|article|aside|audio|b|base|basefont|bdo|" +
        "big|blockquote|body|br|button|canvas|caption|center|cite|code|col|colgroup|" +
        "command|datalist|dd|del|details|dfn|dir|div|dl|dt|em|embed|fieldset|" +
        "figcaption|figure|font|footer|form|frame|frameset|h1|h2|h3|h4|h5|h6|head|" +
        "header|hgroup|hr|html|i|iframe|img|input|ins|keygen|kbd|label|legend|li|" +
        "link|map|mark|menu|meta|meter|nav|noframes|noscript|object|ol|optgroup|" +
        "option|output|p|param|pre|progress|q|rp|rt|ruby|s|samp|script|section|select|" +
        "small|source|span|strike|strong|style|sub|summary|sup|table|tbody|td|" +
        "textarea|tfoot|th|thead|time|title|tr|tt|u|ul|var|video|wbr|xmp"
      ).split("|"),
    );
    var numRe = "\\-?(?:(?:[0-9]+)|(?:[0-9]*\\.[0-9]+))";
    this.$rules = {
      start: [
        {
          token: "comment",
          regex: "\\/\\/.*$",
        },
        {
          token: "comment", // multi line comment
          regex: "\\/\\*",
          next: "comment",
        },
        {
          token: "string", // single line
          regex: '["](?:(?:\\\\.)|(?:[^"\\\\]))*?["]',
        },
        {
          token: "string", // multi line string start
          regex: '["].*\\\\$',
          next: "qqstring",
        },
        {
          token: "string", // single line
          regex: "['](?:(?:\\\\.)|(?:[^'\\\\]))*?[']",
        },
        {
          token: "string", // multi line string start
          regex: "['].*\\\\$",
          next: "qstring",
        },
        {
          token: "constant.numeric",
          regex:
            numRe +
            "(?:ch|cm|deg|em|ex|fr|gd|grad|Hz|in|kHz|mm|ms|pc|pt|px|rad|rem|s|turn|vh|vmax|vmin|vm|vw|%)",
        },
        {
          token: "constant.numeric", // hex6 color
          regex: "#[a-f0-9]{6}",
        },
        {
          token: "constant.numeric", // hex3 color
          regex: "#[a-f0-9]{3}",
        },
        {
          token: "constant.numeric",
          regex: numRe,
        },
        {
          token: ["support.function", "string", "support.function"],
          regex: "(url\\()(.*)(\\))",
        },
        {
          token: function (value) {
            if (properties.hasOwnProperty(value.toLowerCase())) {
              return "support.type";
            }
            if (keywords.hasOwnProperty(value)) {
              return "keyword";
            } else if (constants.hasOwnProperty(value)) {
              return "constant.language";
            } else if (functions.hasOwnProperty(value)) {
              return "support.function";
            } else if (colors.hasOwnProperty(value.toLowerCase())) {
              return "support.constant.color";
            } else if (tags.hasOwnProperty(value.toLowerCase())) {
              return "variable.language";
            } else {
              return "text";
            }
          },
          regex: "\\-?[@a-z_][@a-z0-9_\\-]*",
        },
        {
          token: "variable",
          regex: "[a-z_\\-$][a-z0-9_\\-$]*\\b",
        },
        {
          token: "variable.language",
          regex: "#[a-z0-9-_]+",
        },
        {
          token: "variable.language",
          regex: "\\.[a-z0-9-_]+",
        },
        {
          token: "variable.language",
          regex: ":[a-z0-9-_]+",
        },
        {
          token: "constant",
          regex: "[a-z0-9-_]+",
        },
        {
          token: "keyword.operator",
          regex: "<|>|<=|>=|==|!=|-|%|#|\\+|\\$|\\+|\\*",
        },
        {
          token: "paren.lparen",
          regex: "[[({]",
        },
        {
          token: "paren.rparen",
          regex: "[\\])}]",
        },
        {
          token: "text",
          regex: "\\s+",
        },
        {
          caseInsensitive: true,
        },
      ],
      comment: [
        {
          token: "comment", // closing comment
          regex: "\\*\\/",
          next: "start",
        },
        {
          defaultToken: "comment",
        },
      ],
      qqstring: [
        {
          token: "string",
          regex: '(?:(?:\\\\.)|(?:[^"\\\\]))*?"',
          next: "start",
        },
        {
          token: "string",
          regex: ".+",
        },
      ],
      qstring: [
        {
          token: "string",
          regex: "(?:(?:\\\\.)|(?:[^'\\\\]))*?'",
          next: "start",
        },
        {
          token: "string",
          regex: ".+",
        },
      ],
    };
  };
  oop.inherits(ScssHighlightRules, TextHighlightRules);
  exports.ScssHighlightRules = ScssHighlightRules;
});

define("ace/mode/matching_brace_outdent", [
  "require",
  "exports",
  "module",
  "ace/range",
], function (require, exports, module) {
  "use strict";
  var Range = require("../range").Range;
  var MatchingBraceOutdent = function () {};
  (function () {
    this.checkOutdent = function (line, input) {
      if (!/^\s+$/.test(line)) {
        return false;
      }
      return /^\s*\}/.test(input);
    };
    this.autoOutdent = function (doc, row) {
      var line = doc.getLine(row);
      var match = line.match(/^(\s*\})/);
      if (!match) {
        return 0;
      }
      var column = match[1].length;
      var openBracePos = doc.findMatchingBracket({ row: row, column: column });
      if (!openBracePos || openBracePos.row == row) {
        return 0;
      }
      var indent = this.$getIndent(doc.getLine(openBracePos.row));
      doc.replace(new Range(row, 0, row, column - 1), indent);
    };
    this.$getIndent = function (line) {
      return line.match(/^\s*/)[0];
    };
  }).call(MatchingBraceOutdent.prototype);
  exports.MatchingBraceOutdent = MatchingBraceOutdent;
});

define("ace/mode/behaviour/css", [
  "require",
  "exports",
  "module",
  "ace/lib/oop",
  "ace/mode/behaviour",
  "ace/mode/behaviour/cstyle",
  "ace/token_iterator",
], function (require, exports, module) {
  "use strict";
  var oop = require("../../lib/oop");
  var Behaviour = require("../behaviour").Behaviour;
  var CstyleBehaviour = require("./cstyle").CstyleBehaviour;
  var TokenIterator = require("../../token_iterator").TokenIterator;
  var CssBehaviour = function () {
    this.inherit(CstyleBehaviour);
    this.add(
      "colon",
      "insertion",
      function (state, action, editor, session, text) {
        if (text === ":" && editor.selection.isEmpty()) {
          var cursor = editor.getCursorPosition();
          var iterator = new TokenIterator(session, cursor.row, cursor.column);
          var token = iterator.getCurrentToken();
          if (token && token.value.match(/\s+/)) {
            token = iterator.stepBackward();
          }
          if (token && token.type === "support.type") {
            var line = session.doc.getLine(cursor.row);
            var rightChar = line.substring(cursor.column, cursor.column + 1);
            if (rightChar === ":") {
              return {
                text: "",
                selection: [1, 1],
              };
            }
            if (/^(\s+[^;]|\s*$)/.test(line.substring(cursor.column))) {
              return {
                text: ":;",
                selection: [1, 1],
              };
            }
          }
        }
      },
    );
    this.add(
      "colon",
      "deletion",
      function (state, action, editor, session, range) {
        var selected = session.doc.getTextRange(range);
        if (!range.isMultiLine() && selected === ":") {
          var cursor = editor.getCursorPosition();
          var iterator = new TokenIterator(session, cursor.row, cursor.column);
          var token = iterator.getCurrentToken();
          if (token && token.value.match(/\s+/)) {
            token = iterator.stepBackward();
          }
          if (token && token.type === "support.type") {
            var line = session.doc.getLine(range.start.row);
            var rightChar = line.substring(
              range.end.column,
              range.end.column + 1,
            );
            if (rightChar === ";") {
              range.end.column++;
              return range;
            }
          }
        }
      },
    );
    this.add(
      "semicolon",
      "insertion",
      function (state, action, editor, session, text) {
        if (text === ";" && editor.selection.isEmpty()) {
          var cursor = editor.getCursorPosition();
          var line = session.doc.getLine(cursor.row);
          var rightChar = line.substring(cursor.column, cursor.column + 1);
          if (rightChar === ";") {
            return {
              text: "",
              selection: [1, 1],
            };
          }
        }
      },
    );
    this.add(
      "!important",
      "insertion",
      function (state, action, editor, session, text) {
        if (text === "!" && editor.selection.isEmpty()) {
          var cursor = editor.getCursorPosition();
          var line = session.doc.getLine(cursor.row);
          if (/^\s*(;|}|$)/.test(line.substring(cursor.column))) {
            return {
              text: "!important",
              selection: [10, 10],
            };
          }
        }
      },
    );
  };
  oop.inherits(CssBehaviour, CstyleBehaviour);
  exports.CssBehaviour = CssBehaviour;
});

define("ace/mode/folding/cstyle", [
  "require",
  "exports",
  "module",
  "ace/lib/oop",
  "ace/range",
  "ace/mode/folding/fold_mode",
], function (require, exports, module) {
  "use strict";
  var oop = require("../../lib/oop");
  var Range = require("../../range").Range;
  var BaseFoldMode = require("./fold_mode").FoldMode;
  var FoldMode = (exports.FoldMode = function (commentRegex) {
    if (commentRegex) {
      this.foldingStartMarker = new RegExp(
        this.foldingStartMarker.source.replace(
          /\|[^|]*?$/,
          "|" + commentRegex.start,
        ),
      );
      this.foldingStopMarker = new RegExp(
        this.foldingStopMarker.source.replace(
          /\|[^|]*?$/,
          "|" + commentRegex.end,
        ),
      );
    }
  });
  oop.inherits(FoldMode, BaseFoldMode);
  (function () {
    this.foldingStartMarker = /([\{\[\(])[^\}\]\)]*$|^\s*(\/\*)/;
    this.foldingStopMarker = /^[^\[\{\(]*([\}\]\)])|^[\s\*]*(\*\/)/;
    this.singleLineBlockCommentRe = /^\s*(\/\*).*\*\/\s*$/;
    this.tripleStarBlockCommentRe = /^\s*(\/\*\*\*).*\*\/\s*$/;
    this.startRegionRe = /^\s*(\/\*|\/\/)#?region\b/;
    this._getFoldWidgetBase = this.getFoldWidget;
    this.getFoldWidget = function (session, foldStyle, row) {
      var line = session.getLine(row);
      if (this.singleLineBlockCommentRe.test(line)) {
        if (
          !this.startRegionRe.test(line) &&
          !this.tripleStarBlockCommentRe.test(line)
        ) {
          return "";
        }
      }
      var fw = this._getFoldWidgetBase(session, foldStyle, row);
      if (!fw && this.startRegionRe.test(line)) {
        return "start";
      } // lineCommentRegionStart
      return fw;
    };
    this.getFoldWidgetRange = function (
      session,
      foldStyle,
      row,
      forceMultiline,
    ) {
      var line = session.getLine(row);
      if (this.startRegionRe.test(line)) {
        return this.getCommentRegionBlock(session, line, row);
      }
      var match = line.match(this.foldingStartMarker);
      if (match) {
        var i = match.index;
        if (match[1]) {
          return this.openingBracketBlock(session, match[1], row, i);
        }
        var range = session.getCommentFoldRange(row, i + match[0].length, 1);
        if (range && !range.isMultiLine()) {
          if (forceMultiline) {
            range = this.getSectionRange(session, row);
          } else if (foldStyle != "all") {
            range = null;
          }
        }
        return range;
      }
      if (foldStyle === "markbegin") {
        return;
      }
      var match = line.match(this.foldingStopMarker);
      if (match) {
        var i = match.index + match[0].length;
        if (match[1]) {
          return this.closingBracketBlock(session, match[1], row, i);
        }
        return session.getCommentFoldRange(row, i, -1);
      }
    };
    this.getSectionRange = function (session, row) {
      var line = session.getLine(row);
      var startIndent = line.search(/\S/);
      var startRow = row;
      var startColumn = line.length;
      row = row + 1;
      var endRow = row;
      var maxRow = session.getLength();
      while (++row < maxRow) {
        line = session.getLine(row);
        var indent = line.search(/\S/);
        if (indent === -1) {
          continue;
        }
        if (startIndent > indent) {
          break;
        }
        var subRange = this.getFoldWidgetRange(session, "all", row);
        if (subRange) {
          if (subRange.start.row <= startRow) {
            break;
          } else if (subRange.isMultiLine()) {
            row = subRange.end.row;
          } else if (startIndent == indent) {
            break;
          }
        }
        endRow = row;
      }
      return new Range(
        startRow,
        startColumn,
        endRow,
        session.getLine(endRow).length,
      );
    };
    this.getCommentRegionBlock = function (session, line, row) {
      var startColumn = line.search(/\s*$/);
      var maxRow = session.getLength();
      var startRow = row;
      var re = /^\s*(?:\/\*|\/\/|--)#?(end)?region\b/;
      var depth = 1;
      while (++row < maxRow) {
        line = session.getLine(row);
        var m = re.exec(line);
        if (!m) {
          continue;
        }
        if (m[1]) {
          depth--;
        } else {
          depth++;
        }
        if (!depth) {
          break;
        }
      }
      var endRow = row;
      if (endRow > startRow) {
        return new Range(startRow, startColumn, endRow, line.length);
      }
    };
  }).call(FoldMode.prototype);
});

define("ace/mode/css_completions", ["require", "exports", "module"], function (
  require,
  exports,
  module,
) {
  "use strict";
  var propertyMap = {
    background: { "#$0": 1 },
    "background-color": { "#$0": 1, transparent: 1, fixed: 1 },
    "background-image": { "url('/$0')": 1 },
    "background-repeat": {
      repeat: 1,
      "repeat-x": 1,
      "repeat-y": 1,
      "no-repeat": 1,
      inherit: 1,
    },
    "background-position": {
      bottom: 2,
      center: 2,
      left: 2,
      right: 2,
      top: 2,
      inherit: 2,
    },
    "background-attachment": { scroll: 1, fixed: 1 },
    "background-size": { cover: 1, contain: 1 },
    "background-clip": { "border-box": 1, "padding-box": 1, "content-box": 1 },
    "background-origin": {
      "border-box": 1,
      "padding-box": 1,
      "content-box": 1,
    },
    border: { "solid $0": 1, "dashed $0": 1, "dotted $0": 1, "#$0": 1 },
    "border-color": { "#$0": 1 },
    "border-style": {
      solid: 2,
      dashed: 2,
      dotted: 2,
      double: 2,
      groove: 2,
      hidden: 2,
      inherit: 2,
      inset: 2,
      none: 2,
      outset: 2,
      ridged: 2,
    },
    "border-collapse": { collapse: 1, separate: 1 },
    bottom: { px: 1, em: 1, "%": 1 },
    clear: { left: 1, right: 1, both: 1, none: 1 },
    color: { "#$0": 1, "rgb(#$00,0,0)": 1 },
    cursor: {
      default: 1,
      pointer: 1,
      move: 1,
      text: 1,
      wait: 1,
      help: 1,
      progress: 1,
      "n-resize": 1,
      "ne-resize": 1,
      "e-resize": 1,
      "se-resize": 1,
      "s-resize": 1,
      "sw-resize": 1,
      "w-resize": 1,
      "nw-resize": 1,
    },
    display: {
      none: 1,
      block: 1,
      inline: 1,
      "inline-block": 1,
      "table-cell": 1,
    },
    "empty-cells": { show: 1, hide: 1 },
    float: { left: 1, right: 1, none: 1 },
    "font-family": {
      Arial: 2,
      "Comic Sans MS": 2,
      Consolas: 2,
      "Courier New": 2,
      Courier: 2,
      Georgia: 2,
      Monospace: 2,
      "Sans-Serif": 2,
      "Segoe UI": 2,
      Tahoma: 2,
      "Times New Roman": 2,
      "Trebuchet MS": 2,
      Verdana: 1,
    },
    "font-size": { px: 1, em: 1, "%": 1 },
    "font-weight": { bold: 1, normal: 1 },
    "font-style": { italic: 1, normal: 1 },
    "font-variant": { normal: 1, "small-caps": 1 },
    height: { px: 1, em: 1, "%": 1 },
    left: { px: 1, em: 1, "%": 1 },
    "letter-spacing": { normal: 1 },
    "line-height": { normal: 1 },
    "list-style-type": {
      none: 1,
      disc: 1,
      circle: 1,
      square: 1,
      decimal: 1,
      "decimal-leading-zero": 1,
      "lower-roman": 1,
      "upper-roman": 1,
      "lower-greek": 1,
      "lower-latin": 1,
      "upper-latin": 1,
      georgian: 1,
      "lower-alpha": 1,
      "upper-alpha": 1,
    },
    margin: { px: 1, em: 1, "%": 1 },
    "margin-right": { px: 1, em: 1, "%": 1 },
    "margin-left": { px: 1, em: 1, "%": 1 },
    "margin-top": { px: 1, em: 1, "%": 1 },
    "margin-bottom": { px: 1, em: 1, "%": 1 },
    "max-height": { px: 1, em: 1, "%": 1 },
    "max-width": { px: 1, em: 1, "%": 1 },
    "min-height": { px: 1, em: 1, "%": 1 },
    "min-width": { px: 1, em: 1, "%": 1 },
    overflow: { hidden: 1, visible: 1, auto: 1, scroll: 1 },
    "overflow-x": { hidden: 1, visible: 1, auto: 1, scroll: 1 },
    "overflow-y": { hidden: 1, visible: 1, auto: 1, scroll: 1 },
    padding: { px: 1, em: 1, "%": 1 },
    "padding-top": { px: 1, em: 1, "%": 1 },
    "padding-right": { px: 1, em: 1, "%": 1 },
    "padding-bottom": { px: 1, em: 1, "%": 1 },
    "padding-left": { px: 1, em: 1, "%": 1 },
    "page-break-after": { auto: 1, always: 1, avoid: 1, left: 1, right: 1 },
    "page-break-before": { auto: 1, always: 1, avoid: 1, left: 1, right: 1 },
    position: { absolute: 1, relative: 1, fixed: 1, static: 1 },
    right: { px: 1, em: 1, "%": 1 },
    "table-layout": { fixed: 1, auto: 1 },
    "text-decoration": { none: 1, underline: 1, "line-through": 1, blink: 1 },
    "text-align": { left: 1, right: 1, center: 1, justify: 1 },
    "text-transform": { capitalize: 1, uppercase: 1, lowercase: 1, none: 1 },
    top: { px: 1, em: 1, "%": 1 },
    "vertical-align": { top: 1, bottom: 1 },
    visibility: { hidden: 1, visible: 1 },
    "white-space": {
      nowrap: 1,
      normal: 1,
      pre: 1,
      "pre-line": 1,
      "pre-wrap": 1,
    },
    width: { px: 1, em: 1, "%": 1 },
    "word-spacing": { normal: 1 },
    filter: { "alpha(opacity=$0100)": 1 },
    "text-shadow": { "$02px 2px 2px #777": 1 },
    "text-overflow": { "ellipsis-word": 1, clip: 1, ellipsis: 1 },
    "-moz-border-radius": 1,
    "-moz-border-radius-topright": 1,
    "-moz-border-radius-bottomright": 1,
    "-moz-border-radius-topleft": 1,
    "-moz-border-radius-bottomleft": 1,
    "-webkit-border-radius": 1,
    "-webkit-border-top-right-radius": 1,
    "-webkit-border-top-left-radius": 1,
    "-webkit-border-bottom-right-radius": 1,
    "-webkit-border-bottom-left-radius": 1,
    "-moz-box-shadow": 1,
    "-webkit-box-shadow": 1,
    transform: { "rotate($00deg)": 1, "skew($00deg)": 1 },
    "-moz-transform": { "rotate($00deg)": 1, "skew($00deg)": 1 },
    "-webkit-transform": { "rotate($00deg)": 1, "skew($00deg)": 1 },
  };
  var CssCompletions = function () {};
  (function () {
    this.completionsDefined = false;
    this.defineCompletions = function () {
      if (document) {
        var style = document.createElement("c").style;
        for (var i in style) {
          if (typeof style[i] !== "string") {
            continue;
          }
          var name = i.replace(/[A-Z]/g, function (x) {
            return "-" + x.toLowerCase();
          });
          if (!propertyMap.hasOwnProperty(name)) {
            propertyMap[name] = 1;
          }
        }
      }
      this.completionsDefined = true;
    };
    this.getCompletions = function (state, session, pos, prefix) {
      if (!this.completionsDefined) {
        this.defineCompletions();
      }
      if (state === "ruleset" || session.$mode.$id == "ace/mode/scss") {
        var line = session.getLine(pos.row).substr(0, pos.column);
        var inParens = /\([^)]*$/.test(line);
        if (inParens) {
          line = line.substr(line.lastIndexOf("(") + 1);
        }
        if (/:[^;]+$/.test(line)) {
          /([\w\-]+):[^:]*$/.test(line);
          return this.getPropertyValueCompletions(state, session, pos, prefix);
        } else {
          return this.getPropertyCompletions(
            state,
            session,
            pos,
            prefix,
            inParens,
          );
        }
      }
      return [];
    };
    this.getPropertyCompletions = function (
      state,
      session,
      pos,
      prefix,
      skipSemicolon,
    ) {
      skipSemicolon = skipSemicolon || false;
      var properties = Object.keys(propertyMap);
      return properties.map(function (property) {
        return {
          caption: property,
          snippet: property + ": $0" + (skipSemicolon ? "" : ";"),
          meta: "property",
          score: 1000000,
        };
      });
    };
    this.getPropertyValueCompletions = function (state, session, pos, prefix) {
      var line = session.getLine(pos.row).substr(0, pos.column);
      var property = (/([\w\-]+):[^:]*$/.exec(line) || {})[1];
      if (!property) {
        return [];
      }
      var values = [];
      if (
        property in propertyMap &&
        typeof propertyMap[property] === "object"
      ) {
        values = Object.keys(propertyMap[property]);
      }
      return values.map(function (value) {
        return {
          caption: value,
          snippet: value,
          meta: "property value",
          score: 1000000,
        };
      });
    };
  }).call(CssCompletions.prototype);
  exports.CssCompletions = CssCompletions;
});

define("ace/mode/scss", [
  "require",
  "exports",
  "module",
  "ace/lib/oop",
  "ace/mode/text",
  "ace/mode/scss_highlight_rules",
  "ace/mode/matching_brace_outdent",
  "ace/mode/behaviour/css",
  "ace/mode/folding/cstyle",
  "ace/mode/css_completions",
], function (require, exports, module) {
  "use strict";
  var oop = require("../lib/oop");
  var TextMode = require("./text").Mode;
  var ScssHighlightRules = require("./scss_highlight_rules").ScssHighlightRules;
  var MatchingBraceOutdent =
    require("./matching_brace_outdent").MatchingBraceOutdent;
  var CssBehaviour = require("./behaviour/css").CssBehaviour;
  var CStyleFoldMode = require("./folding/cstyle").FoldMode;
  var CssCompletions = require("./css_completions").CssCompletions;
  var Mode = function () {
    this.HighlightRules = ScssHighlightRules;
    this.$outdent = new MatchingBraceOutdent();
    this.$behaviour = new CssBehaviour();
    this.$completer = new CssCompletions();
    this.foldingRules = new CStyleFoldMode();
  };
  oop.inherits(Mode, TextMode);
  (function () {
    this.lineCommentStart = "//";
    this.blockComment = { start: "/*", end: "*/" };
    this.getNextLineIndent = function (state, line, tab) {
      var indent = this.$getIndent(line);
      var tokens = this.getTokenizer().getLineTokens(line, state).tokens;
      if (tokens.length && tokens[tokens.length - 1].type == "comment") {
        return indent;
      }
      var match = line.match(/^.*\{\s*$/);
      if (match) {
        indent += tab;
      }
      return indent;
    };
    this.checkOutdent = function (state, line, input) {
      return this.$outdent.checkOutdent(line, input);
    };
    this.autoOutdent = function (state, doc, row) {
      this.$outdent.autoOutdent(doc, row);
    };
    this.getCompletions = function (state, session, pos, prefix) {
      return this.$completer.getCompletions(state, session, pos, prefix);
    };
    this.$id = "ace/mode/scss";
  }).call(Mode.prototype);
  exports.Mode = Mode;
});
(function () {
  window.require(["ace/mode/scss"], function (m) {
    if (typeof module == "object" && typeof exports == "object" && module) {
      module.exports = m;
    }
  });
})();
