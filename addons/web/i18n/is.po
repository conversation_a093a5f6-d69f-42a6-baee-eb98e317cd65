# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web
#
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2022
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 13:50+0000\n"
"PO-Revision-Date: 2022-09-22 05:55+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2022\n"
"Language-Team: Icelandic (https://www.transifex.com/odoo/teams/41243/is/)\n"
"Language: is\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n % 10 != 1 || n % 100 == 11);\n"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
msgid " records"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "# Code editor"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
msgid "%d days ago"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
msgid "%d hours ago"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
msgid "%d minutes ago"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
msgid "%d months ago"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
msgid "%d years ago"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/file_upload/file_upload_service.js:0
msgid "%s Files"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/formatters.js:0
msgid "%s records"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
msgid "'%s' is not a correct date"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/dates.js:0
msgid "'%s' is not a correct date or datetime"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/time.js:0
msgid "'%s' is not a correct date, datetime nor time"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
msgid "'%s' is not a correct datetime"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
msgid "'%s' is not a correct float"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
msgid "'%s' is not a correct integer"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
msgid "'%s' is not a correct monetary field"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/time.js:0
msgid "'%s' is not convertible to date, datetime nor time"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/basic/basic_model.js:0
msgid "'%s' is unsynchronized with '%s'."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/file_upload/file_upload_progress_record.js:0
msgid "(%s/%sMB)"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_edition.xml:0
msgid "(Community Edition)"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
msgid "(change)"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
msgid "(create)"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/burger_menu/mobile_switch_company_menu/mobile_switch_company_menu.xml:0
msgid "(current)"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_bar.js:0
#: code:addons/web/static/src/search/search_bar/search_bar.js:0
msgid "(no result)"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_button/view_button.xml:0
msgid "(no string)"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "(nolabel)"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
msgid ""
") format(\"woff\");\n"
"                        font-weight: normal;\n"
"                        font-style: normal;\n"
"                    }"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.js:0
msgid "+ Add %s"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
msgid "+ KEY"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "07/08/2020"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "08/07/2020"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#: code:addons/web/static/src/views/fields/formatters.js:0
msgid "1 record"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span class=\"text-nowrap\">$ 2,887.50</span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span class=\"text-nowrap\">$ <span class=\"oe_currency_value\">\n"
"                                                       22,137.50</span></span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span class=\"text-nowrap\">$ <span class=\"oe_currency_value\">11,750.00</span></span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span class=\"text-nowrap\">$ <span class=\"oe_currency_value\">7,500.00</span></span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span class=\"text-nowrap\">1,500.00</span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span class=\"text-nowrap\">2,350.00</span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span class=\"text-nowrap\">Tax 15%</span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span class=\"w-100 o_force_ltr\" itemprop=\"streetAddress\">77 Santa Barbara\n"
"                                                   Rd<br/>Pleasant Hill CA 94523<br/>United States</span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span id=\"line_tax_ids\">15.00%</span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span itemprop=\"name\">Deco Addict</span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>$ <span class=\"oe_currency_value\">19,250.00</span></span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>5.000</span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Amount</span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Description</span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span>Invoice</span>\n"
"                           <span>INV/2020/07/0003</span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Payment terms: 30 Days</span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Quantity</span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Taxes</span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Unit Price</span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span>[FURN_8220] Four Person Desk<br/>\n"
"                                       Four person modern office workstation</span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span>[FURN_8999] Three-Seat Sofa<br/>\n"
"                                       Three Seater Sofa with Lounger in Steel Grey Colour</span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<strong>Due Date:</strong>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<strong>Invoice Date:</strong>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<strong>Subtotal</strong>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<strong>Total</strong>"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
msgid "=ilike"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
msgid "=like"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
msgid ""
"@font-face {\n"
"                        font-family: \"font\";\n"
"                        src: url(data:font/ttf;base64,"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/custom_favorite_item/custom_favorite_item.js:0
msgid "A filter with same name already exists."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/custom_favorite_item.js:0
#: code:addons/web/static/src/search/custom_favorite_item/custom_favorite_item.js:0
msgid "A name for your favorite filter is required."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/action_service.js:0
msgid "A popup window has been blocked. You may need to change your browser settings to allow popup windows for this page."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "ALL"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "ANY"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/binary/binary_field.js:0
#: code:addons/web/static/src/views/fields/image/image_field.js:0
#: code:addons/web/static/src/views/fields/many2many_binary/many2many_binary_field.js:0
msgid "Accepted file extensions"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
msgid "Access Denied"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
msgid "Access Error"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
msgid "Access to all Enterprise Apps"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/ace/ace_field.js:0
msgid "Ace Editor"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/components/action_menus.js:0
#: code:addons/web/static/src/search/action_menus/action_menus.xml:0
#: code:addons/web/static/src/search/cog_menu/action_menus_items.xml:0
#: code:addons/web/static/src/views/form/status_bar_buttons/status_bar_buttons.xml:0
msgid "Action"
msgstr "Aðgerð"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_button/view_button.xml:0
msgid "Action ID:"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
msgid "Activate Assets Debugging"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
msgid "Activate Tests Assets Debugging"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_providers.js:0
msgid "Activate debug mode (with assets)"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_dev_tool.xml:0
msgid "Activate the developer mode"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_dev_tool.xml:0
msgid "Activate the developer mode (with assets)"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_dev_tool.xml:0
msgid "Activate the developer mode (with tests assets)"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/search_bar_menu/search_bar_menu.js:0
#: code:addons/web/static/src/views/fields/x2many/x2many_field.js:0
#: code:addons/web/static/src/views/kanban/kanban_column_quick_create.xml:0
#: code:addons/web/static/src/views/kanban/kanban_record_quick_create.xml:0
msgid "Add"
msgstr "Bæta við"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar_menu/search_bar_menu.js:0
#: code:addons/web/static/src/search/search_bar_menu/search_bar_menu.xml:0
msgid "Add Custom Filter"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/custom_group_by_item/custom_group_by_item.xml:0
msgid "Add Custom Group"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.xml:0
msgid "Add a Property"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition_selection.xml:0
msgid "Add a Value"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Add a condition"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/list/list_renderer.js:0
#: code:addons/web/static/src/views/list/list_renderer.xml:0
msgid "Add a line"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Add branch"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/kanban/kanban_column_quick_create.xml:0
msgid "Add column"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector.xml:0
msgid "Add condition"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/datetime/datetime_field.xml:0
msgid "Add end date"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Add filter"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_fields.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Add new value"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Add node"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
msgid "Add qweb directive context"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/datetime/datetime_field.xml:0
msgid "Add start date"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_fields.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Add tag"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/x2many/x2many_field.js:0
msgid "Add: %s"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/components/action_menus.js:0
msgid "Additionnal actions"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/signature_dialog.xml:0
msgid "Adopt & Sign"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/signature_dialog.js:0
msgid "Adopt Your Signature"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/confirmation_dialog/confirmation_dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
msgid "Alert"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_arch_parser.js:0
#: code:addons/web/static/src/search/search_panel/search_panel.xml:0
#: code:addons/web/static/src/views/list/list_controller.xml:0
msgid "All"
msgstr "Allt"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_common/calendar_common_popover.js:0
#: code:addons/web/static/src/views/calendar/calendar_common/calendar_common_renderer.js:0
msgid "All day"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
msgid "All users"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
msgid "Almond"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
msgid "Among the"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/file_upload/file_upload_service.js:0
msgid "An error occured while uploading."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
msgid "An error occurred"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
msgid "And more"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Any"
msgstr ""

#. module: web
#: model:ir.model.fields,help:web.field_base_document_layout__report_header
msgid "Appears by default on the top right corner of your printed documents (report header)."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/custom_group_by_item/custom_group_by_item.xml:0
msgid "Apply"
msgstr "Virkja"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/form/form_controller.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
msgid "Archive"
msgstr "Setja í geymslu"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/kanban/kanban_header.xml:0
msgid "Archive All"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/kanban/kanban_header.js:0
msgid "Are you sure that you want to archive all the records from this column?"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/list/list_controller.js:0
msgid "Are you sure that you want to archive all the selected records?"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/form/form_controller.js:0
msgid "Are you sure that you want to archive this record?"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web/static/src/search/search_bar_menu/search_bar_menu.js:0
msgid "Are you sure that you want to remove this filter?"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/list/list_controller.js:0
msgid "Are you sure you want to delete these records?"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/kanban/kanban_header.js:0
msgid "Are you sure you want to delete this column?"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
msgid "Are you sure you want to delete this property field? It will be removed for everyone using the \"%s\" %s."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_controller.js:0
#: code:addons/web/static/src/views/form/form_controller.js:0
#: code:addons/web/static/src/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
msgid "Are you sure you want to delete this record?"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
msgid "Are you sure you want to perform the following update on those"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_fields.xml:0
#: code:addons/web/static/src/views/fields/datetime/datetime_field.xml:0
msgid "Arrow"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_fields.xml:0
#: code:addons/web/static/src/views/fields/datetime/datetime_field.xml:0
msgid "Arrow icon"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/model_field_selector_popover.js:0
#: code:addons/web/static/src/views/fields/dynamic_placeholder_popover.xml:0
msgid "As a default text when no value are set"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
msgid "Ascending"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2many_tags_avatar/many2many_tags_avatar_field.xml:0
#: code:addons/web/static/src/views/fields/many2one_avatar/many2one_avatar_field.xml:0
msgid "Assign"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2many_binary/many2many_binary_field.xml:0
msgid "Attach"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/kanban/kanban_cover_image_dialog.xml:0
msgid "Attachment"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/attachment_image/attachment_image_field.js:0
msgid "Attachment Image"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
msgid "Auto"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
msgid "Available fields"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
msgid "Avatar"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__layout_background_image
msgid "Background Image"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/badge/badge_field.js:0
msgid "Badge"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/badge_selection/badge_selection_field.js:0
msgid "Badges"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
msgid "Bar Chart"
msgstr ""

#. module: web
#: model:ir.model,name:web.model_base
msgid "Base"
msgstr "Stofnupphæð"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
msgid "Become Superuser"
msgstr ""

#. module: web
#. odoo-python
#: code:addons/web/controllers/export.py:0
msgid "Binary fields can not be exported to Excel unless their content is base64-encoded. That does not seem to be the case for %s."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/image/image_field.xml:0
#: code:addons/web/static/src/views/fields/signature/signature_field.xml:0
msgid "Binary file"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
msgid "Blue"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/boolean_icon/boolean_icon_field.js:0
msgid "Boolean Icon"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
msgid "Bugfixes guarantee"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_button/view_button.xml:0
msgid "Button"
msgstr "Hnappur"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_button/view_button.xml:0
msgid "Button Type:"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/signature_dialog.xml:0
msgid "By clicking Adopt & Sign, I agree that the chosen signature/initials will be a valid electronic representation of my hand-written signature/initials for all purposes when it is used on documents, including legally binding contracts."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/formatters.js:0
msgid "Bytes"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/utils.js:0
msgid "Bytes|Kb|Mb|Gb|Tb|Pb|Eb|Zb|Yb"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/command_palette.xml:0
msgid "CMD"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/command_palette.xml:0
msgid "CTRL"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/phone/phone_field.xml:0
msgid "Call"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/confirmation_dialog/confirmation_dialog.js:0
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#: code:addons/web/static/src/core/signature/signature_dialog.xml:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web/static/src/search/search_bar_menu/search_bar_menu.js:0
#: code:addons/web/static/src/views/calendar/quick_create/calendar_quick_create.xml:0
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Cancel"
msgstr "Hætta við"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/file_upload/file_upload_progress_bar.xml:0
msgid "Cancel Upload"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
msgid "Change default:"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
msgid "Change graph"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/boolean/boolean_field.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
msgid "Checkbox"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2many_checkboxes/many2many_checkboxes_field.js:0
msgid "Checkboxes"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/colorpicker.js:0
msgid "Choose"
msgstr "Choose"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/file_input/file_input.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Choose File"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu.js:0
msgid "Choose a debug command..."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_fields.xml:0
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/binary/binary_field.xml:0
#: code:addons/web/static/src/views/fields/image/image_field.xml:0
#: code:addons/web/static/src/views/fields/pdf_viewer/pdf_viewer_field.xml:0
#: code:addons/web/static/src/views/view_dialogs/select_create_dialog.xml:0
msgid "Clear"
msgstr "Clear"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/statusbar/statusbar_field.js:0
msgid "Clickable"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#: code:addons/web/static/src/core/dialog/dialog.xml:0
#: code:addons/web/static/src/core/domain_selector_dialog/domain_selector_dialog.xml:0
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#: code:addons/web/static/src/core/model_field_selector/model_field_selector_popover.xml:0
#: code:addons/web/static/src/core/notifications/notification.xml:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/dialog.xml:0
#: code:addons/web/static/src/views/fields/dynamic_placeholder_popover.xml:0
#: code:addons/web/static/src/views/fields/relational_utils.xml:0
#: code:addons/web/static/src/views/kanban/kanban_column_examples_dialog.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#: code:addons/web/static/src/views/view_dialogs/form_view_dialog.xml:0
#: code:addons/web/static/src/views/view_dialogs/select_create_dialog.xml:0
msgid "Close"
msgstr "Loka"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/burger_menu/burger_menu.xml:0
msgid "Close menu"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Colors"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/kanban/kanban_renderer.js:0
msgid "Column %s"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/burger_menu/mobile_switch_company_menu/mobile_switch_company_menu.xml:0
#: model:ir.model,name:web.model_res_company
msgid "Companies"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__company_id
msgid "Company"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__company_details
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Company Details"
msgstr ""

#. module: web
#: model:ir.model,name:web.model_base_document_layout
msgid "Company Document Layout"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__logo
msgid "Company Logo"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__name
msgid "Company Name"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__report_header
msgid "Company Tagline"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.frontend_layout
msgid "Company name"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web/static/src/search/search_bar_menu/search_bar_menu.xml:0
msgid "Comparison"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
msgid "Condition:"
msgstr ""

#. module: web
#: model:ir.actions.act_window,name:web.action_base_document_layout_configurator
msgid "Configure your document layout"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector_dialog/domain_selector_dialog.js:0
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
msgid "Confirm"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/confirmation_dialog/confirmation_dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/views/calendar/calendar_controller.js:0
#: code:addons/web/static/src/views/list/list_confirmation_dialog.js:0
msgid "Confirmation"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_handlers.js:0
msgid "Connection lost. Trying to reconnect..."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_handlers.js:0
msgid "Connection restored. You are back online."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#: code:addons/web/static/src/views/view_button/view_button.xml:0
msgid "Context:"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/x2many/x2many_field.xml:0
msgid "Control panel buttons"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/control_panel_model_extension.js:0
msgid ""
"Control panel model extension failed to evaluate domain:\n"
"%(error)s"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/copy_clipboard/copy_clipboard_field.js:0
msgid "Copied"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/copy_clipboard/copy_clipboard_field.js:0
msgid "Copy"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/copy_clipboard/copy_clipboard_field.js:0
msgid "Copy Multiline Text to Clipboard"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/copy_clipboard/copy_clipboard_field.js:0
msgid "Copy Text to Clipboard"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/copy_clipboard/copy_clipboard_field.js:0
msgid "Copy URL to Clipboard"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
msgid "Copy error to clipboard"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/copy_clipboard/copy_clipboard_field.js:0
msgid "Copy to Clipboard"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.frontend_layout
msgid "Copyright &amp;copy;"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_edition.xml:0
msgid "Copyright © 2004"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/ajax.js:0
msgid "Could not connect to the server"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/image/image_field.js:0
#: code:addons/web/static/src/views/fields/signature/signature_field.js:0
msgid "Could not display the selected image"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/pdf_viewer/pdf_viewer_field.js:0
msgid "Could not display the selected pdf"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/image_url/image_url_field.js:0
msgid "Could not display the specified image url."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/utils.js:0
msgid "Could not serialize XML"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/kanban/kanban_record.js:0
msgid "Could not set the cover image: incorrect field (\"%s\") is provided in the view."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/barcode/barcode_scanner.js:0
msgid "Could not start scanning. "
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/kanban/progress_bar_hook.js:0
#: code:addons/web/static/src/views/utils.js:0
msgid "Count"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__country_id
msgid "Country"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/calendar/calendar_year/calendar_year_popover.xml:0
#: code:addons/web/static/src/views/calendar/quick_create/calendar_quick_create.xml:0
#: code:addons/web/static/src/views/fields/many2one/many2one_field.xml:0
#: code:addons/web/static/src/views/kanban/kanban_record_quick_create.js:0
msgid "Create"
msgstr "Stofna"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_tags.js:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
msgid "Create \"%s\""
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
msgid "Create \"<strong>%s</strong>\""
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/relational_utils.js:0
msgid "Create %s"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
msgid "Create and Edit..."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/relational_utils.js:0
msgid "Create and edit..."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
msgid "Create: "
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
msgid "Create: %s"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__create_uid
msgid "Created by"
msgstr "Búið til af"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__create_date
msgid "Created on"
msgstr "Stofnað þann"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
msgid "Creation Date:"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
msgid "Creation User:"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
msgid "Cumulative"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/statusbar/statusbar_field.xml:0
msgid "Current state"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/progress_bar/progress_bar_field.js:0
msgid "Current value field"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__custom_colors
msgid "Custom Colors"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
msgid "Cyan"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Dark blue"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Dark purple"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Database"
msgstr "Database"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.neutralize_banner
msgid "Database neutralized for testing: no emails sent, etc."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/datetime/datetime_field.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
msgid "Date"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/datetime/datetime_field.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
msgid "Date & Time"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/datetime/datetime_field.js:0
msgid "Date Range"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#: code:addons/web/static/src/views/calendar/calendar_controller.js:0
msgid "Day"
msgstr "Dagur"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_providers.js:0
msgid "Deactivate debug mode"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_dev_tool.xml:0
msgid "Deactivate the developer mode"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu.js:0
msgid "Debug tools..."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
msgid "Decimal"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
msgid "Default"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
msgid "Default State"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
msgid "Default Value"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/signature/signature_field.js:0
msgid "Default font"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/model_field_selector_popover.js:0
#: code:addons/web/static/src/views/fields/dynamic_placeholder_popover.xml:0
msgid "Default text is used when no values are set"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/model_field_selector_popover.js:0
#: code:addons/web/static/src/views/fields/dynamic_placeholder_popover.xml:0
msgid "Default value"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
msgid "Default:"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/image/image_field.js:0
msgid "Delay the apparition of the zoomed image with a value in milliseconds"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/tags_list/tags_list.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/calendar/calendar_common/calendar_common_popover.xml:0
#: code:addons/web/static/src/views/calendar/calendar_controller.js:0
#: code:addons/web/static/src/views/fields/many2many_binary/many2many_binary_field.xml:0
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#: code:addons/web/static/src/views/form/form_controller.js:0
#: code:addons/web/static/src/views/kanban/kanban_header.js:0
#: code:addons/web/static/src/views/kanban/kanban_header.xml:0
#: code:addons/web/static/src/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
msgid "Delete"
msgstr "Eyða"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
msgid "Delete Property Field"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web/static/src/search/search_bar_menu/search_bar_menu.xml:0
msgid "Delete item"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Delete node"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/list/list_renderer.xml:0
msgid "Delete row"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
msgid "Descending"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/list/list_renderer.js:0
msgid "Different currencies cannot be aggregated"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/float/float_field.js:0
#: code:addons/web/static/src/views/fields/float_toggle/float_toggle_field.js:0
msgid "Digits"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2many_tags/many2many_tags_field.js:0
#: code:addons/web/static/src/views/fields/many2one/many2one_field.js:0
msgid "Disable creation"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2one/many2one_field.js:0
msgid "Disable opening"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/float_toggle/float_toggle_field.js:0
msgid "Disable readonly"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector_dialog/domain_selector_dialog.js:0
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/js/widgets/colorpicker.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/views/fields/many2one/many2one_field.xml:0
#: code:addons/web/static/src/views/fields/relational_utils.xml:0
#: code:addons/web/static/src/views/fields/translation_dialog.xml:0
#: code:addons/web/static/src/views/form/form_controller.xml:0
#: code:addons/web/static/src/views/kanban/kanban_cover_image_dialog.xml:0
#: code:addons/web/static/src/views/list/list_controller.xml:0
#: code:addons/web/static/src/webclient/settings_form_view/settings_confirmation_dialog.xml:0
msgid "Discard"
msgstr "Hætta við"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/form/form_error_dialog/form_error_dialog.xml:0
#: code:addons/web/static/src/views/form/form_status_indicator/form_status_indicator.xml:0
msgid "Discard changes"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/kanban/kanban_record_quick_create.js:0
#: model:ir.model.fields,field_description:web.field_base_document_layout__display_name
msgid "Display Name"
msgstr "Nafn"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/radio/radio_field.js:0
msgid "Display horizontally"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/datetime/datetime_field.js:0
msgid "Displays a warning icon if the input dates are in the future."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/file_upload/file_upload_progress_bar.js:0
msgid "Do you really want to cancel the upload of %s?"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.js:0
msgid "Do you really want to delete this export template?"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__external_report_layout_id
msgid "Document Template"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/widgets/documentation_link/documentation_link.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
msgid "Documentation"
msgstr "Documentation"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector_dialog/domain_selector_dialog.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/views/fields/domain/domain_field.js:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
msgid "Domain"
msgstr "Lén"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector_dialog/domain_selector_dialog.js:0
msgid "Domain is invalid. Please correct it"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Domain node"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
msgid "Domain not properly formed"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
msgid "Domain not supported"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
msgid "Domain:"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/ui/block_ui.js:0
msgid "Don't leave yet,"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/misc.js:0
msgid "Don't leave yet,<br />it's still loading..."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/binary/binary_field.xml:0
#: code:addons/web/static/src/views/fields/many2many_binary/many2many_binary_field.xml:0
msgid "Download"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Download PDF Preview"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/pivot/pivot_controller.xml:0
msgid "Download xlsx"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
msgid "Draw"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/form/form_controller.js:0
msgid "Duplicate"
msgstr "Afrita"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/datetime/datetime_field.js:0
msgid "Earliest accepted date"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_common/calendar_common_popover.xml:0
#: code:addons/web/static/src/views/calendar/quick_create/calendar_quick_create.xml:0
#: code:addons/web/static/src/views/fields/binary/binary_field.xml:0
#: code:addons/web/static/src/views/fields/image/image_field.xml:0
#: code:addons/web/static/src/views/fields/pdf_viewer/pdf_viewer_field.xml:0
#: code:addons/web/static/src/views/kanban/kanban_header.xml:0
#: code:addons/web/static/src/views/kanban/kanban_record_quick_create.xml:0
msgid "Edit"
msgstr "Skrifa"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
msgid "Edit Action"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
msgid "Edit Domain"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/debug_items.js:0
msgid "Edit SearchView"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/debug_items.js:0
msgid "Edit View: "
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/domain/domain_field.js:0
msgid "Edit in dialog"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/progress_bar/progress_bar_field.js:0
msgid "Edit max value"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/kanban/kanban_header.js:0
msgid "Edit: %s"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/progress_bar/progress_bar_field.js:0
msgid "Editable"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/email/email_field.js:0
#: model:ir.model.fields,field_description:web.field_base_document_layout__email
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Email"
msgstr "Tölvupóstur"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_invite_users.js:0
msgid "Empty email address"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
msgid "Enable profiling"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/image/image_field.js:0
msgid "Enable zoom"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/datetime/datetime_field.js:0
msgid "End date field"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_invite_users.xml:0
msgid "Enter e-mail address"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/highlight_text/form_label_highlight_text.xml:0
msgid "Enterprise"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/file_upload/file_upload_service.js:0
msgid "Error"
msgstr "Villa!"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/kanban/kanban_column_quick_create.xml:0
msgid "Esc"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_model.js:0
msgid "Everybody's calendars"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_model.js:0
msgid "Everything"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/pivot/pivot_controller.xml:0
msgid "Expand all"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/list/list_controller.js:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
msgid "Export"
msgstr "Export"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/list/list_controller.xml:0
msgid "Export All"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.js:0
msgid "Export Data"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
msgid "Export Format:"
msgstr ""

#. module: web
#. odoo-python
#: code:addons/web/controllers/export.py:0
msgid "Exporting grouped data to csv is not supported."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_fields.xml:0
msgid "Expression"
msgstr ""

#. module: web
#. odoo-python
#. odoo-javascript
#: code:addons/web/controllers/export.py:0
#: code:addons/web/static/src/views/list/list_controller.js:0
msgid "External ID"
msgstr "External ID"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "External link"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/search_panel/search_panel.xml:0
msgid "FILTER"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/float_factor/float_factor_field.js:0
#: code:addons/web/static/src/views/fields/float_toggle/float_toggle_field.js:0
msgid "Factor"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/control_panel_model_extension.js:0
msgid "Failed to evaluate search context"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/search_model.js:0
msgid ""
"Failed to evaluate the context: %(context)s.\n"
"%(error)s"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/search_model.js:0
msgid ""
"Failed to evaluate the domain: %(domain)s.\n"
"%(error)s"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
msgid "False"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/boolean_favorite/boolean_favorite_field.js:0
msgid "Favorite"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web/static/src/search/search_bar_menu/search_bar_menu.xml:0
msgid "Favorites"
msgstr "Eftirlæti"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
msgid "Field Type"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
msgid "Field:"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
msgid "Fields to export"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/binary/binary_field.js:0
msgid "File"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/file_handler.js:0
msgid "File upload"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/custom_favorite_item.js:0
msgid "Filter with same name already exists."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web/static/src/search/search_bar_menu/search_bar_menu.xml:0
msgid "Filters"
msgstr "Síur"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/pivot/pivot_controller.xml:0
msgid "Flip axis"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/float/float_field.js:0
msgid "Float"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/kanban/kanban_header.xml:0
msgid "Fold"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/statusbar/statusbar_field.js:0
msgid "Fold field"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_field_selector/model_field_selector.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Followed by"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__font
msgid "Font"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/font_selection/font_selection_field.js:0
msgid "Font Selection"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Footer"
msgstr ""

#. module: web
#: model:ir.model.fields,help:web.field_base_document_layout__report_footer
msgid "Footer text displayed at the bottom of all reports."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/pivot/pivot_renderer.js:0
msgid ""
"For Excel compatibility, data cannot be exported if there are more than 16384 columns.\n"
"\n"
"Tip: try to flip axis, filter further or reduce the number of measures."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/widgets/week_days/week_days.js:0
msgid "Fri"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
msgid "Full Name"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Fushia"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_edition.xml:0
msgid "GNU LGPL Licensed"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/debug_items.js:0
msgid "Get View"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
msgid "Get this feature and much more with Odoo Enterprise!"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/url/url_field.xml:0
msgid "Go to URL"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_view.js:0
msgid "Graph"
msgstr "Graph"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Green"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web/static/src/search/search_bar_menu/search_bar_menu.xml:0
#: code:addons/web/static/src/views/pivot/pivot_group_by_menu.xml:0
msgid "Group By"
msgstr "Hópa eftir"

#. module: web
#: model:ir.model,name:web.model_ir_http
msgid "HTTP Routing"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/handle/handle_field.js:0
msgid "Handle"
msgstr ""

#. module: web
#: model:ir.model.fields,help:web.field_base_document_layout__company_details
msgid "Header text displayed at the top of all reports."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Hide in Kanban"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2many_tags/many2many_tags_field.xml:0
msgid "Hide in kanban"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Hit DOWN to navigate to the list below"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Hit ENTER to"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Hit ENTER to CREATE"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
msgid "I am sure about this."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
msgid "I want to update data (import-compatible export)"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__id
msgid "ID"
msgstr "Auðkenni"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
msgid "ID:"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/datetime/datetime_field.js:0
msgid "ISO-formatted date (e.g. \"2018-12-31\") or \"today\"."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/boolean_icon/boolean_icon_field.js:0
msgid "Icon"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/basic/basic_model.js:0
msgid "If you change %s or %s, the synchronization will be reapplied and the data will be modified."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/attachment_image/attachment_image_field.xml:0
#: code:addons/web/static/src/views/fields/image/image_field.js:0
#: code:addons/web/static/src/views/fields/image_url/image_url_field.js:0
#: code:addons/web/static/src/views/fields/image_url/image_url_field.xml:0
msgid "Image"
msgstr "Mynd"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/remaining_days/remaining_days_field.xml:0
msgid "In"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/datetime/datetime_field.js:0
msgid "Increment used in the minutes selection dropdown."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/integer/integer_field.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
msgid "Integer"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2one/many2one_field.xml:0
#: code:addons/web/static/src/views/fields/properties/property_value.xml:0
msgid "Internal link"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
msgid "Interval"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
msgid "Invalid data"
msgstr ""

#. module: web
#. odoo-python
#: code:addons/web/controllers/database.py:0
msgid "Invalid database name. Only alphanumerical characters, underscore, hyphen and dot are allowed."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
msgid "Invalid domain"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_invite_users.js:0
msgid "Invalid email address: %(address)s"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_invite_users.js:0
msgid "Invalid email addresses: %(2 addresses)s"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_invite_users.js:0
msgid "Invalid email addresses: %(addresses)s"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_field_selector/model_field_selector.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Invalid field chain"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/model_field_selector_popover.js:0
msgid "Invalid field chain. You may have used a non-existing field name or followed a non-relational field."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/quick_create/calendar_quick_create.js:0
msgid "Invalid fields"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/basic_relational_model.js:0
#: code:addons/web/static/src/views/kanban/kanban_record_quick_create.js:0
#: code:addons/web/static/src/views/relational_model.js:0
msgid "Invalid fields: "
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_invite_users.js:0
msgid "Invite"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_invite_users.xml:0
msgid "Invite New Users"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_invite_users.js:0
msgid "Inviting..."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
msgid ""
"It is possible that the \"t-call\" time does not correspond to the overall time of the\n"
"            template. Because the global time (in the drop down) does not take into account the\n"
"            duration which is not in the rendering (look for the template, read, inheritance,\n"
"            compilation...). During rendering, the global time also takes part of the time to make\n"
"            the profile as well as some part not logged in the function generated by the qweb."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/kanban/kanban_column_examples_dialog.js:0
msgid "Kanban Examples"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/kanban/kanban_record.js:0
msgid "Kanban: no action for type: "
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/label_selection/label_selection_field.js:0
#: code:addons/web/static/src/views/fields/state_selection/state_selection_field.js:0
msgid "Label Selection"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
msgid "Label:"
msgstr ""

#. module: web
#. odoo-python
#: code:addons/web/controllers/session.py:0
msgid "Languages"
msgstr "Tungumál"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/image/image_field.js:0
#: code:addons/web/static/src/views/fields/image_url/image_url_field.js:0
#: code:addons/web/static/src/views/fields/signature/signature_field.js:0
msgid "Large"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__write_uid
msgid "Last Updated by"
msgstr "Síðast uppfært af"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__write_date
msgid "Last Updated on"
msgstr "Síðast uppfært þann"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
msgid "Latest Modification Date:"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
msgid "Latest Modification by:"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/datetime/datetime_field.js:0
msgid "Latest accepted date"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Layout"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__layout_background
msgid "Layout Background"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
msgid "Leave the Developer Tools"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Light blue"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
msgid "Line Chart"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
msgid "Load"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_dev_tool.xml:0
msgid "Load demo data"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
msgid "Load more... ("
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#: code:addons/web/static/src/webclient/loading_indicator/loading_indicator.xml:0
msgid "Loading"
msgstr "Hleð"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Loading, please wait..."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_selector/model_selector.js:0
#: code:addons/web/static/src/core/ui/block_ui.js:0
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.js:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
msgid "Loading..."
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Log in"
msgstr "Innskráningar nafn"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Log in as superuser"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#: model_terms:ir.ui.view,arch_db:web.login_successful
msgid "Log out"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_bold
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
#: model_terms:ir.ui.view,arch_db:web.external_layout_striped
#: model_terms:ir.ui.view,arch_db:web.frontend_layout
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Logo"
msgstr "Lógó"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__logo_primary_color
msgid "Logo Primary Color"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__logo_secondary_color
msgid "Logo Secondary Color"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
msgid "MailDeliveryException"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/form/form_controller.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#: code:addons/web/static/src/views/kanban/kanban_controller.xml:0
#: code:addons/web/static/src/views/list/list_controller.xml:0
#: code:addons/web/static/src/views/pivot/pivot_controller.xml:0
msgid "Main actions"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/debug_items.js:0
msgid "Manage Attachments"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Manage Databases"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
msgid "Manage Filters"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2one_barcode/many2one_barcode_field.js:0
msgid "Many2OneBarcode"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
msgid "Many2many"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/fields/many2one/many2one_field.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
msgid "Many2one"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Match"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Match records with"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Match records with the following rule:"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/gauge/gauge_field.js:0
#: code:addons/web/static/src/views/fields/progress_bar/progress_bar_field.js:0
msgid "Max value field"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/gauge/gauge_field.js:0
msgid "Max: "
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/ui/block_ui.js:0
#: code:addons/web/static/src/legacy/js/core/misc.js:0
msgid "Maybe you should consider reloading the application by pressing F5..."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/view.xml:0
msgid "Measures"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/image/image_field.js:0
#: code:addons/web/static/src/views/fields/image_url/image_url_field.js:0
#: code:addons/web/static/src/views/fields/signature/signature_field.js:0
msgid "Medium"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Medium blue"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/quick_create/calendar_quick_create.js:0
msgid "Meeting Subject"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/quick_create/calendar_quick_create.xml:0
msgid "Meeting Subject:"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/burger_menu/burger_menu.xml:0
#: model:ir.model,name:web.model_ir_ui_menu
msgid "Menu"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_button/view_button.xml:0
msgid "Method:"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
msgid "Missing Record"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
msgid "Mobile support"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/ace/ace_field.js:0
msgid "Mode"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/domain/domain_field.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
msgid "Model"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
msgid "Model Record Rules"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
msgid "Model:"
msgstr ""

#. module: web
#: model:ir.model,name:web.model_ir_model
msgid "Models"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#: code:addons/web/static/src/views/view_button/view_button.xml:0
msgid "Modifiers:"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/search_bar/search_bar.js:0
msgid "Modify Condition"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/widgets/week_days/week_days.js:0
msgid "Mon"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/monetary/monetary_field.js:0
msgid "Monetary"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#: code:addons/web/static/src/views/calendar/calendar_controller.js:0
msgid "Month"
msgstr "Mánuður"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/statusbar/statusbar_field.xml:0
#: code:addons/web/static/src/views/form/button_box/button_box.xml:0
msgid "More"
msgstr "More"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/statusbar/statusbar_field.js:0
msgid "Move to %s..."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/statusbar/statusbar_field.js:0
msgid "Move to next %s"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/text/text_field.js:0
msgid "Multiline Text"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
msgid "My Odoo.com account"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "NONE"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/basic/basic_model.js:0
#: code:addons/web/static/src/views/form/form_controller.js:0
#: code:addons/web/static/src/views/form/form_controller.xml:0
#: code:addons/web/static/src/views/kanban/kanban_controller.xml:0
#: code:addons/web/static/src/views/list/list_controller.xml:0
#: code:addons/web/static/src/views/view_dialogs/select_create_dialog.xml:0
msgid "New"
msgstr "Nýtt"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
msgid "New %s"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_controller.js:0
#: code:addons/web/static/src/views/calendar/quick_create/calendar_quick_create.js:0
msgid "New Event"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.xml:0
msgid "New Property"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
msgid "New design"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.js:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
msgid "New template"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/relational_utils.js:0
msgid "New:"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2one/many2one_field.js:0
msgid "New: %s"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/pager/pager.xml:0
msgid "Next"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/datetime/datetime_picker.js:0
msgid "Next century"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/datetime/datetime_picker.js:0
msgid "Next decade"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/datetime/datetime_picker.js:0
msgid "Next month"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Next page"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/datetime/datetime_picker.js:0
msgid "Next year"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_bar.js:0
#: code:addons/web/static/src/search/search_bar/search_bar.js:0
#: code:addons/web/static/src/views/kanban/kanban_header.js:0
#: code:addons/web/static/src/views/list/list_renderer.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
msgid "No"
msgstr "No"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_value.js:0
#: code:addons/web/static/src/views/fields/properties/property_value.xml:0
#: code:addons/web/static/src/views/relational_model.js:0
msgid "No Access"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
msgid "No Update:"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/client_actions.js:0
msgid "No action with id '%s' could be found"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
msgid "No color"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/default_providers.js:0
msgid "No command found"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2one/many2one_field.js:0
msgid "No create edit"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/list/list_renderer.js:0
msgid "No currency provided"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_renderer.js:0
msgid "No data"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/no_content_helpers.xml:0
msgid "No data to display"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu.js:0
msgid "No debug command found"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/barcode/barcode_scanner.js:0
msgid "No device can be found."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
msgid "No match found."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/menus/menu_providers.js:0
msgid "No menu found"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2one/many2one_field.js:0
msgid "No quick create"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/search_panel/search_panel.xml:0
msgid "No quick filter available."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_selector/model_selector.js:0
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.js:0
#: code:addons/web/static/src/views/fields/formatters.js:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
msgid "No records"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/view_dialogs/select_create_dialog.js:0
msgid "No records found!"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_tags.js:0
msgid "No result"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/command_palette.js:0
#: code:addons/web/static/src/core/select_menu/select_menu.xml:0
msgid "No result found"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/relational_model.js:0
msgid "No valid record to save"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/action_service.js:0
msgid "No view of type '%s' could be found in the current action."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/kanban/kanban_header.js:0
#: code:addons/web/static/src/views/list/list_renderer.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
msgid "None"
msgstr "Ekkert"

#. module: web
#. odoo-python
#: code:addons/web/models/models.py:0
msgid "Not Set"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/statusbar/statusbar_field.xml:0
msgid "Not active state"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/statusbar/statusbar_field.xml:0
msgid "Not active state, click to change it"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_button/view_button.xml:0
msgid "Object:"
msgstr "Object:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_edition.xml:0
#: model_terms:ir.ui.view,arch_db:web.brand_promotion_message
msgid "Odoo"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
msgid "Odoo Client Error"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
msgid "Odoo Error"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
msgid "Odoo Network Error"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/widgets/notification_alert/notification_alert.xml:0
msgid "Odoo Push notifications have been blocked. Go to your browser settings to allow them."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_edition.xml:0
msgid "Odoo S.A."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
msgid "Odoo Server Error"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/public/error_notifications.js:0
msgid "Odoo Session Expired"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
msgid "Odoo Warning"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/barcode/barcode_scanner.js:0
msgid "Odoo needs your authorization first."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/list/list_controller.js:0
msgid "Of the %s records selected, only the first %s have been archived/unarchived."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/form/form_error_dialog/form_error_dialog.xml:0
msgid "Oh snap!"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/confirmation_dialog/confirmation_dialog.js:0
#: code:addons/web/static/src/core/dialog/dialog.xml:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web/static/src/public/error_notifications.js:0
#: code:addons/web/static/src/views/calendar/calendar_year/calendar_year_popover.xml:0
#: code:addons/web/static/src/views/view_dialogs/form_view_dialog.xml:0
#: code:addons/web/static/src/webclient/actions/action_dialog.xml:0
msgid "Ok"
msgstr "Ok"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "On change:"
msgstr ""

#. module: web
#. odoo-python
#: code:addons/web/controllers/home.py:0
msgid "Only employees can access this database. Please contact the administrator."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/list/list_controller.js:0
msgid "Only the first %s records have been deleted (out of %s selected)"
msgstr ""

#. module: web
#. odoo-python
#: code:addons/web/models/models.py:0
msgid "Only types %(supported_types)s are supported for category (found type %(field_type)s)"
msgstr ""

#. module: web
#. odoo-python
#: code:addons/web/models/models.py:0
msgid "Only types %(supported_types)s are supported for filter (found type %(field_type)s)"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
msgid "Only you"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/debug_items.js:0
msgid "Open View"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/relational_utils.js:0
msgid "Open:"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
msgid "Open: "
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_controller.js:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
msgid "Open: %s"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition_selection.xml:0
msgid "Option Name"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Orange"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/kanban/progress_bar_hook.js:0
msgid "Other"
msgstr "Annað"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/pdf_viewer/pdf_viewer_field.js:0
msgid "PDF Viewer"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/pdf_viewer/pdf_viewer_field.xml:0
msgid "PDF file"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_striped
msgid ""
"Page:\n"
"                    <span class=\"page\"/>\n"
"                    of\n"
"                    <span class=\"topage\"/>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
msgid "Page: <span class=\"page\"/> / <span class=\"topage\"/>"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/pager/pager.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Pager"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__paperformat_id
msgid "Paper format"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__partner_id
msgid "Partner"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Password"
msgstr "Lykilorð"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_invite_users.xml:0
msgid "Pending Invitations:"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/percent_pie/percent_pie_field.js:0
msgid "PercentPie"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/percentage/percentage_field.js:0
msgid "Percentage"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/phone/phone_field.js:0
#: model:ir.model.fields,field_description:web.field_base_document_layout__phone
msgid "Phone"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/colorpicker.js:0
msgid "Pick a color"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
msgid "Pie Chart"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
msgid "Pie chart cannot mix positive and negative numbers. Try to change your domain to only display positive results"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/pivot/pivot_view.js:0
msgid "Pivot"
msgstr "Pivot"

#. module: web
#. odoo-python
#: code:addons/web/controllers/pivot.py:0
msgid "Pivot %(title)s (%(model_name)s)"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/pivot/pivot_controller.xml:0
msgid "Pivot settings"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/ui/block_ui.js:0
msgid "Please be patient."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/list/list_renderer.js:0
msgid "Please click on the \"save\" button first"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
msgid "Please complete your properties before adding a new one"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.js:0
msgid "Please enter save field list name"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.js:0
msgid "Please select fields to save export list..."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
msgid "Please use the copy button to report the error to your support service."
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"Please use the following communication for your payment: <b><span>\n"
"                           INV/2020/07/0003</span></b>"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2one/many2one_field.js:0
msgid "Please, scan again!"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.brand_promotion_message
msgid "Powered by %s%s"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Powered by <span>Odoo</span>"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
msgid "Preferences"
msgstr "Preferences"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/signature/signature_field.js:0
msgid "Prefill with"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/kanban/kanban_column_quick_create.xml:0
msgid "Press"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2many_tags/many2many_tags_field.js:0
msgid "Prevent color edition"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__preview
msgid "Preview"
msgstr ""

#. module: web
#: model:ir.actions.report,name:web.action_report_externalpreview
msgid "Preview External Report"
msgstr ""

#. module: web
#: model:ir.actions.report,name:web.action_report_internalpreview
msgid "Preview Internal Report"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/image/image_field.js:0
msgid "Preview image"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/signature/signature_field.js:0
msgid "Preview image field"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__preview_logo
msgid "Preview logo"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_field_selector/model_field_selector_popover.xml:0
#: code:addons/web/static/src/core/pager/pager.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Previous"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
msgid "Previous Period"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
msgid "Previous Year"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/datetime/datetime_picker.js:0
msgid "Previous century"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/datetime/datetime_picker.js:0
msgid "Previous decade"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Previous menu"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/datetime/datetime_picker.js:0
msgid "Previous month"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Previous page"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/datetime/datetime_picker.js:0
msgid "Previous year"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__primary_color
msgid "Primary Color"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/components/action_menus.js:0
#: code:addons/web/static/src/search/action_menus/action_menus.xml:0
#: code:addons/web/static/src/search/cog_menu/action_menus_items.xml:0
#: code:addons/web/static/src/webclient/actions/reports/report_action.xml:0
msgid "Print"
msgstr "Prenta"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/components/action_menus.js:0
msgid "Printing options"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/priority/priority_field.js:0
#: code:addons/web/static/src/views/fields/priority/priority_field.xml:0
msgid "Priority"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/file_upload/file_upload_progress_record.js:0
msgid "Processing..."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/progress_bar/progress_bar_field.js:0
msgid "Progress Bar"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
msgid "Properties"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
msgid "Property %s"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
msgid "Property Name"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Purple"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
msgid "Q1"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
msgid "Q2"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
msgid "Q3"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
msgid "Q4"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
msgid "Quarter"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/kanban/kanban_header.xml:0
msgid "Quick add"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/search_panel/search_panel.xml:0
msgid "Quick filters will become available if the records shown can be filtered."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.js:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
msgid "Quick search: %s"
msgstr ""

#. module: web
#: model:ir.model,name:web.model_ir_qweb_field_image
#: model:ir.model,name:web.model_ir_qweb_field_image_url
msgid "Qweb Field Image"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/colorpicker.xml:0
msgid "RGB"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/colorpicker.xml:0
msgid "RGBA"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/fields/radio/radio_field.js:0
msgid "Radio"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/float_toggle/float_toggle_field.js:0
msgid "Range"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
msgid "Raspberry"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
msgid "Record qweb"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
msgid "Record sql"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
msgid "Record traces"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
msgid "Recording..."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Red"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/reference/reference_field.js:0
msgid "Reference"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
msgid "Refresh"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
msgid "Regenerate Assets Bundles"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/model_field_selector_popover.js:0
msgid "Relation not allowed"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_field_selector/model_field_selector_popover.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Relation to follow"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
msgid "Relation:"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/x2many/x2many_field.js:0
msgid "Relational table"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/remaining_days/remaining_days_field.js:0
msgid "Remaining Days"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#: code:addons/web/static/src/views/fields/relational_utils.xml:0
#: code:addons/web/static/src/views/form/form_controller.xml:0
msgid "Remove"
msgstr "Fjarlægja"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/kanban/kanban_cover_image_dialog.xml:0
msgid "Remove Cover Image"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#: code:addons/web/static/src/views/fields/properties/property_definition_selection.xml:0
msgid "Remove Property"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.js:0
msgid "Remove field"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_fields.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Remove tag"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
msgid "Remove this favorite from the list"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/action_service.js:0
msgid "Report"
msgstr "Skýrsla"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__report_footer
msgid "Report Footer"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__report_layout_id
msgid "Report Layout"
msgstr ""

#. module: web
#: model:ir.actions.report,name:web.action_report_layout_preview
msgid "Report Layout Preview"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/public/error_notifications.js:0
msgid "Request timeout"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector.xml:0
msgid "Reset domain"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Reset to logo colors"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/datetime/datetime_field.js:0
msgid "Rounding"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/clickbot/clickbot_loader.js:0
msgid "Run Click Everywhere Test"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_providers.js:0
#: code:addons/web/static/src/webclient/debug_items.js:0
msgid "Run JS Mobile Tests"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_providers.js:0
#: code:addons/web/static/src/webclient/debug_items.js:0
msgid "Run JS Tests"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/search_panel/search_panel.xml:0
msgid "SEE RESULT"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/signature/signature_field.xml:0
msgid "SIGNATURE"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Salmon pink"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/widgets/week_days/week_days.js:0
msgid "Sat"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/search/custom_favorite_item/custom_favorite_item.xml:0
#: code:addons/web/static/src/views/fields/relational_utils.xml:0
#: code:addons/web/static/src/views/fields/translation_dialog.xml:0
#: code:addons/web/static/src/views/form/form_controller.xml:0
#: code:addons/web/static/src/views/list/list_controller.xml:0
#: code:addons/web/static/src/webclient/settings_form_view/settings_confirmation_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Save"
msgstr "Vista"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/relational_utils.xml:0
#: code:addons/web/static/src/views/view_dialogs/form_view_dialog.xml:0
msgid "Save & Close"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/relational_utils.xml:0
#: code:addons/web/static/src/views/view_dialogs/form_view_dialog.xml:0
msgid "Save & New"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
msgid "Save as:"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/custom_favorite_item/custom_favorite_item.xml:0
msgid "Save current search"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
msgid "Save default"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/form/form_status_indicator/form_status_indicator.xml:0
msgid "Save manually"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2one/many2one_field.xml:0
msgid "Scan barcode"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#: code:addons/web/static/src/webclient/settings_form_view/settings/settings_app.xml:0
msgid "Search"
msgstr "Leita"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.js:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
msgid "Search More..."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_selector/model_selector.js:0
msgid "Search a Model..."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Search a field..."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/default_providers.js:0
msgid "Search for a command..."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/menus/menu_providers.js:0
msgid "Search for a menu..."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Search for records"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2one_avatar/many2one_avatar_field.js:0
msgid "Search user..."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2many_tags_avatar/many2many_tags_avatar_field.js:0
msgid "Search users..."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/command_palette.js:0
#: code:addons/web/static/src/core/model_field_selector/model_field_selector_popover.xml:0
#: code:addons/web/static/src/core/select_menu/select_menu.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#: code:addons/web/static/src/webclient/settings_form_view/settings_form_view.xml:0
msgid "Search..."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.js:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
msgid "Search: %s"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__secondary_color
msgid "Secondary Color"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
msgid "See details"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/kanban/kanban_column_quick_create.xml:0
msgid "See examples"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/kanban/kanban_cover_image_dialog.xml:0
#: code:addons/web/static/src/views/view_dialogs/select_create_dialog.xml:0
msgid "Select"
msgstr "Select"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Select <i class=\"fa fa-database\" role=\"img\" aria-label=\"Database\" title=\"Database\"/>"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition_selection.xml:0
msgid "Select Default"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
msgid "Select a model to add a filter."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/debug_items.js:0
msgid "Select a view"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/list/list_controller.xml:0
msgid "Select all"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/datetime/datetime_picker.js:0
msgid "Select century"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/datetime/datetime_picker.js:0
msgid "Select decade"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
msgid "Select field"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/datetime/datetime_picker.js:0
msgid "Select month"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/relational_utils.js:0
msgid "Select records"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/datetime/datetime_picker.js:0
msgid "Select year"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/domain/domain_field.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
msgid "Selected records"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#: code:addons/web/static/src/views/fields/selection/selection_field.js:0
msgid "Selection"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
msgid "Selection:"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/email/email_field.xml:0
msgid "Send Email"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/debug_items.js:0
msgid "Set Defaults"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/timezone_mismatch/timezone_mismatch_field.js:0
msgid "Set a timezone on your user"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/state_selection/state_selection_field.js:0
msgid "Set kanban state as %s"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/priority/priority_field.js:0
msgid "Set priority..."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/kanban/kanban_header.xml:0
#: code:addons/web/static/src/webclient/settings_form_view/settings_form_controller.js:0
msgid "Settings"
msgstr "Stillingar"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/custom_favorite_item/custom_favorite_item.xml:0
msgid "Share with all users"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
msgid "Shortcuts"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/control_panel/control_panel.js:0
msgid "Show %s view"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
msgid "Show sub-fields"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/image/image_field.js:0
#: code:addons/web/static/src/views/fields/image_url/image_url_field.js:0
#: code:addons/web/static/src/views/fields/signature/signature_field.js:0
msgid "Size"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Size:"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/image/image_field.js:0
#: code:addons/web/static/src/views/fields/image_url/image_url_field.js:0
#: code:addons/web/static/src/views/fields/signature/signature_field.js:0
msgid "Small"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/ajax.js:0
msgid "Something happened while trying to contact the server, check that the server is online and that you still have a working network connection."
msgstr ""

#. module: web
#. odoo-python
#: code:addons/web/controllers/binary.py:0
msgid "Something horrible happened"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
msgid "Sort graph"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_button/view_button.xml:0
msgid "Special:"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
msgid "Stacked"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/datetime/datetime_field.js:0
msgid "Start date field"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/fields/properties/property_tags.js:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
msgid "Start typing..."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/stat_info/stat_info_field.js:0
msgid "Stat Info"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/statusbar/statusbar_field.js:0
msgid "Status"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/settings_confirmation_dialog.xml:0
msgid "Stay Here"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/form/form_error_dialog/form_error_dialog.xml:0
msgid "Stay here"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/float/float_field.js:0
#: code:addons/web/static/src/views/fields/integer/integer_field.js:0
msgid "Step"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/ui/block_ui.js:0
#: code:addons/web/static/src/legacy/js/core/misc.js:0
msgid "Still loading..."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/misc.js:0
msgid "Still loading...<br />Please be patient."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
msgid "Style"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/scss_error_dialog.js:0
msgid "Style error"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
msgid "Styles"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_model.js:0
msgid "Sum"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_model.js:0
msgid "Sum (%s)"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/widgets/week_days/week_days.js:0
msgid "Sun"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
msgid "Support"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
msgid "Syntax error"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
msgid "TIP"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/fields/many2many_tags/many2many_tags_field.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
msgid "Tags"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/ui/block_ui.js:0
msgid "Take a minute to get a coffee,"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/misc.js:0
msgid "Take a minute to get a coffee,<br />because it's loading..."
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__vat
msgid "Tax ID"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
msgid "Teal"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
msgid "Template:"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/char/char_field.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
msgid "Text"
msgstr ""

#. module: web
#: model:ir.model.fields,help:web.field_base_document_layout__vat
msgid "The Tax Identification Number. Values here will be validated based on the country format. You can use '/' to indicate that the partner is not subject to tax."
msgstr ""

#. module: web
#. odoo-python
#: code:addons/web/controllers/action.py:0
msgid "The action %r does not exist."
msgstr ""

#. module: web
#. odoo-python
#: code:addons/web/controllers/export.py:0
msgid "The content of this cell is too long for an XLSX file (more than %s characters). Please use the CSV format for this export."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
msgid "The field is empty, there's nothing to save."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#: code:addons/web/static/src/public/error_notifications.js:0
msgid "The operation was interrupted. This usually means that the current operation is taking too much time."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/signature/signature_field.js:0
msgid "The selected field will be used to pre-fill the signature"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/file_handler.js:0
msgid "The selected file exceed the maximum file size of %s."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
msgid "The style compilation failed, see the error below. This is an administrator or developer error that must be fixed for the entire database before continuing working."
msgstr ""

#. module: web
#. odoo-python
#: code:addons/web/controllers/export.py:0
msgid "There are too many rows (%s rows, limit: %s) to export as Excel 2007-2013 (.xlsx) format. Consider splitting the export."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/kanban/kanban_cover_image_dialog.xml:0
msgid "There is no available image to be set as cover."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
msgid "There was a problem while uploading your file"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/file_handler.js:0
msgid "There was a problem while uploading your file."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/date_picker.js:0
msgid "This date is in the future. Make sure this is what you expect."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/datetime/datetime_field.xml:0
msgid "This date is on the future. Make sure it is what you expected."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector.xml:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
msgid "This domain is not supported."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
msgid "This field is already first"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
msgid "This field is already last"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
msgid "This file is invalid. Please select an image."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web/static/src/search/search_bar_menu/search_bar_menu.js:0
msgid "This filter is global and will be removed for everybody if you continue."
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.preview_externalreport
msgid "This is a sample of an external report."
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.preview_internalreport
msgid "This is a sample of an internal report."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_tags.js:0
msgid "This tag is already available"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
msgid "This update will only consider the records of the current page."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/widgets/week_days/week_days.js:0
msgid "Thu"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/float_time/float_time_field.js:0
msgid "Time"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/timezone_mismatch/timezone_mismatch_field.js:0
msgid ""
"Timezone Mismatch : This timezone is different from that of your browser.\n"
"Please, set the same timezone as your browser's to avoid time discrepancies in your system."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/gauge/gauge_field.js:0
msgid "Title"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_controller.xml:0
#: code:addons/web/static/src/views/fields/remaining_days/remaining_days_field.xml:0
msgid "Today"
msgstr "Í dag"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/boolean_toggle/boolean_toggle_field.js:0
msgid "Toggle"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/control_panel/control_panel.xml:0
msgid "Toggle Dropdown"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/burger_menu/burger_menu.xml:0
msgid "Toggle menu"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/remaining_days/remaining_days_field.xml:0
msgid "Tomorrow"
msgstr ""

#. module: web
#. odoo-python
#: code:addons/web/models/models.py:0
msgid "Too many items to display."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_model.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
msgid "Total"
msgstr "Samtals"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/translation_dialog.js:0
msgid "Translate: %s"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
msgid "True"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/no_content_helpers.xml:0
msgid ""
"Try to add some records, or make sure that there is no\n"
"                    active filter in the search bar."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/widgets/week_days/week_days.js:0
msgid "Tue"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/float/float_field.js:0
#: code:addons/web/static/src/views/fields/float_time/float_time_field.js:0
#: code:addons/web/static/src/views/fields/float_toggle/float_toggle_field.js:0
#: code:addons/web/static/src/views/fields/integer/integer_field.js:0
msgid "Type"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/dynamic_placeholder_popover.xml:0
msgid "Type a default text or press ENTER"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
msgid "Type your name to sign"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
msgid "Type:"
msgstr "Type:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/url/url_field.js:0
msgid "URL"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/reports/utils.js:0
msgid "Unable to find Wkhtmltopdf on this system. The report will be shown in html."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/form/form_controller.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
msgid "Unarchive"
msgstr "Taka úr geymslu"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/kanban/kanban_header.xml:0
msgid "Unarchive All"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_service.js:0
msgid "Uncaught CORS Error"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_service.js:0
msgid "Uncaught Javascript Error"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_service.js:0
msgid "Uncaught Promise"
msgstr ""

#. module: web
#. odoo-python
#. odoo-javascript
#: code:addons/web/controllers/export.py:0
#: code:addons/web/static/src/views/calendar/calendar_model.js:0
#: code:addons/web/static/src/views/graph/graph_model.js:0
msgid "Undefined"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/kanban/kanban_header.xml:0
msgid "Unfold"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_service.js:0
msgid ""
"Unknown CORS error\n"
"\n"
"An unknown CORS error occured.\n"
"The error probably originates from a JavaScript file served from a different origin.\n"
"(Opening your browser console might give you a hint on the error.)"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/py_utils.js:0
msgid "Unknown nonliteral type "
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/control_panel/control_panel.xml:0
msgid "Unnamed"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/settings_confirmation_dialog.js:0
#: code:addons/web/static/src/webclient/settings_form_view/settings_form_view.xml:0
msgid "Unsaved changes"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/list/list_controller.xml:0
msgid "Unselect All"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_view.js:0
#: code:addons/web/static/src/views/pivot/pivot_view.js:0
msgid "Untitled"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/search_panel/search_panel.xml:0
msgid "Update the filters in the search bar to display more records."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
msgid "Update to:"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
msgid "Upgrade now"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
msgid "Upgrade to enterprise"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
msgid "Upgrade to future versions"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/kanban/kanban_cover_image_dialog.xml:0
msgid "Upload and Set"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/binary/binary_field.xml:0
#: code:addons/web/static/src/views/fields/pdf_viewer/pdf_viewer_field.xml:0
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Upload your file"
msgstr "Upload your file"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2many_binary/many2many_binary_field.xml:0
msgid "Uploaded"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2many_binary/many2many_binary_field.xml:0
msgid "Uploading"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2many_binary/many2many_binary_field.js:0
msgid "Uploading error"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/file_handler.xml:0
msgid "Uploading..."
msgstr "Uploading..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/file_upload/file_upload_progress_record.js:0
msgid "Uploading... (%s%)"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/kanban/kanban_column_quick_create.js:0
msgid "Use This For My Kanban"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/custom_favorite_item/custom_favorite_item.xml:0
msgid "Use by default"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2many_tags/many2many_tags_field.js:0
msgid "Use colors"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu.xml:0
#: model:ir.model,name:web.model_res_users
msgid "User"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
msgid "User Error"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
msgid "Validation Error"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/gauge/gauge_field.js:0
msgid "Value: "
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
msgid "Values"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/form/form_label.js:0
#: code:addons/web/static/src/views/form/setting/setting.xml:0
msgid "Values set here are company-specific."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
msgid "Variation"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "View %s"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
msgid "View Access Rights"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
msgid "View Fields"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
msgid "View In Kanban"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/debug_items.js:0
msgid "View Metadata"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
msgid "View Record Rules"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "View switcher"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
msgid "Violet"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/search/search_bar_menu/search_bar_menu.js:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#: code:addons/web/static/src/views/fields/translation_button.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
msgid "Warning"
msgstr "Viðvörun"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/datetime/datetime_field.js:0
msgid "Warning for future dates"
msgstr ""

#. module: web
#. odoo-python
#: code:addons/web/models/models.py:0
msgid "Warnings"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.qunit_mobile_suite
msgid "Web Mobile Tests"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.qunit_suite
msgid "Web Tests"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__website
msgid "Website Link"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/widgets/week_days/week_days.js:0
msgid "Wed"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#: code:addons/web/static/src/views/calendar/calendar_common/calendar_common_renderer.js:0
#: code:addons/web/static/src/views/calendar/calendar_controller.js:0
msgid "Week"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/datetime/datetime_picker.js:0
msgid "Week numbers"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/effects/effect_service.js:0
msgid "Well Done!"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
msgid "Widget:"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/settings_form_controller.js:0
msgid "Would you like to save your changes?"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/iframe_wrapper/iframe_wrapper_field.js:0
msgid "Wrap raw html within an iframe"
msgstr ""

#. module: web
#. odoo-python
#: code:addons/web/controllers/home.py:0
msgid "Wrong login/password"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
msgid "XML ID:"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#: code:addons/web/static/src/views/calendar/calendar_controller.js:0
msgid "Year"
msgstr "Ár"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "Yellow"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_bar.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.js:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#: code:addons/web/static/src/views/kanban/kanban_header.js:0
#: code:addons/web/static/src/views/list/list_renderer.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
msgid "Yes"
msgstr "Yes"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/remaining_days/remaining_days_field.xml:0
msgid "Yesterday"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_successful
msgid "You are logged in."
msgstr ""

#. module: web
#. odoo-python
#: code:addons/web/controllers/binary.py:0
msgid "You are not allowed to upload an attachment here."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
msgid "You can not create a new property."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/model_field_selector_popover.js:0
msgid "You cannot follow relations for this field chain construction"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
msgid "You do not have access to the model \"%s\"."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/ui/block_ui.js:0
msgid "You may not believe it,"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/misc.js:0
msgid "You may not believe it,<br />but the application is actually loading..."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_tags.js:0
msgid "You need to be able to edit parent first to add property tags"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
msgid "You need to be able to edit parent first to configure property fields"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/translation_button.js:0
msgid "You need to save this new record before editing the translation. Do you want to proceed?"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/dynamic_placeholder_hook.js:0
msgid "You need to select a model before opening the dynamic placeholder selector."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/reports/utils.js:0
msgid "You need to start Odoo with at least two workers to print a pdf version of the reports."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/reports/utils.js:0
msgid "You should upgrade your version of Wkhtmltopdf to at least 0.12.0 in order to get a correct display of headers and footers as well as support for table-breaking between pages."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#: code:addons/web/static/src/public/error_notifications.js:0
msgid "Your Odoo session expired. The current page is about to be refreshed."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/reports/utils.js:0
msgid "Your installation of Wkhtmltopdf seems to be broken. The report will be shown in html."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/image/image_field.js:0
msgid "Zoom delay"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
msgid "a day ago"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
msgid "about a minute ago"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
msgid "about a month ago"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
msgid "about a year ago"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
msgid "about an hour ago"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector.xml:0
#: code:addons/web/static/src/core/domain_selector/domain_selector_nodes.js:0
msgid "all"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "all records"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/search_model.js:0
msgid "and"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector.xml:0
#: code:addons/web/static/src/core/domain_selector/domain_selector_nodes.js:0
msgid "any"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
msgid "are valid for this update."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/many2one/many2one_field.xml:0
msgid "as a new"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "at:"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/ui/block_ui.js:0
msgid "because it's loading..."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/ui/block_ui.js:0
msgid "but the application is actually loading..."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
msgid "child of"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
msgid "contains"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/search_bar/search_bar.js:0
msgid "date"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/remaining_days/remaining_days_field.xml:0
msgid "days"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/remaining_days/remaining_days_field.xml:0
msgid "days ago"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
msgid "does not contain"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
msgid "doesn't contain"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/network/download.js:0
msgid "downloading..."
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "e.g. Global Business Solutions"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "for:"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
msgid "greater than"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
msgid "greater than or equal to"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/colorpicker.xml:0
msgid "hex"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_common/calendar_common_popover.js:0
msgid "hour"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_common/calendar_common_popover.js:0
msgid "hours"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
msgid "in"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector.xml:0
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "is"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
msgid "is No"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
msgid "is Yes"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
msgid "is after"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
msgid "is after or equal to"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
msgid "is before"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
msgid "is before or equal to"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
msgid "is between"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
msgid "is equal to"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
msgid "is not"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
msgid "is not ="
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
msgid "is not equal to"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
msgid "is not set"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
msgid "is set"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/ui/block_ui.js:0
msgid "it's still loading..."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/utils.js:0
#: code:addons/web/static/src/views/fields/formatters.js:0
msgid "kMGTPE"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
msgid "less than"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
msgid "less than a minute ago"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
msgid "less than or equal to"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
msgid "like"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/menus/menu_providers.js:0
msgid "menus"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_common/calendar_common_popover.js:0
msgid "minute"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_common/calendar_common_popover.js:0
msgid "minutes"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_invite_users.xml:0
msgid "more"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
msgid "ms"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/default_providers.js:0
msgid "no description provided"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_nodes.js:0
msgid "none"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "not"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_nodes.js:0
msgid "not %s"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_nodes.js:0
msgid "not all"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
msgid "not in"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
msgid "not like"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_fields.js:0
msgid "not set"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
msgid "not set (false)"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "of the following rules:"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "of:"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
msgid "on any screen to show shortcut overlays and"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/action_model.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_model.js:0
msgid "or"
msgstr "eða"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
msgid "parent of"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2many_tags_avatar/many2many_tags_avatar_field.xml:0
msgid "props.placeholder"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
msgid "query"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
msgid "record(s)"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
msgid "records?"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
msgid "remaining)"
msgstr "remaining)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
msgid "search"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/list/list_controller.xml:0
msgid "selected"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
msgid "selected records,"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector.xml:0
#: code:addons/web/static/src/core/domain_selector/domain_selector_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "set"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
msgid "set (true)"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/kanban/kanban_column_quick_create.xml:0
msgid "to discard"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/command_palette.xml:0
msgid "to open in new tab"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/command_palette.xml:0
msgid "to open,"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
msgid "to trigger a shortcut."
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
msgid "type a default text or press ENTER"
msgstr ""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
msgid "— press"
msgstr ""
